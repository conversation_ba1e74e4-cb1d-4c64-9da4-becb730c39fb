<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="turbo-visit-control" content="reload">
  <title>Action Controller: Exception caught</title>
  <style>
    body {
      background-color: #FAFAFA;
      color: #333;
      color-scheme: light dark;
      supported-color-schemes: light dark;
      margin: 0px;
    }

    body, p, ol, ul, td {
      font-family: helvetica, verdana, arial, sans-serif;
      font-size:   13px;
      line-height: 18px;
    }

    pre {
      font-size: 11px;
      white-space: pre-wrap;
    }

    pre.box {
      border: 1px solid #EEE;
      padding: 10px;
      margin: 0px;
      width: 958px;
    }

    header {
      color: #F0F0F0;
      background: #C00;
      padding: 0.5em 1.5em;
    }

    h1 {
      overflow-wrap: break-word;
      margin: 0.2em 0;
      line-height: 1.1em;
      font-size: 2em;
    }

    h2 {
      color: #C00;
      line-height: 25px;
    }

    code.traces {
      font-size: 11px;
    }

    .response-heading, .request-heading {
      margin-top: 30px;
    }

    .exception-message {
      padding: 8px 0;
    }

    .exception-message .message {
      margin-bottom: 8px;
      line-height: 25px;
      font-size: 1.5em;
      font-weight: bold;
      color: #C00;
    }

    .details {
      border: 1px solid #D0D0D0;
      border-radius: 4px;
      margin: 1em 0px;
      display: block;
      max-width: 978px;
    }

    .summary {
      padding: 8px 15px;
      border-bottom: 1px solid #D0D0D0;
      display: block;
    }

    a.summary {
      color: #F0F0F0;
      text-decoration: none;
      background: #C52F24;
      border-bottom: none;
    }

    .details pre {
      margin: 5px;
      border: none;
    }

    #container {
      box-sizing: border-box;
      width: 100%;
      padding: 0 1.5em;
    }

    .source * {
      margin: 0px;
      padding: 0px;
    }

    .source {
      border: 1px solid #D9D9D9;
      background: #ECECEC;
      max-width: 978px;
    }

    .source pre {
      padding: 10px 0px;
      border: none;
    }

    .source .data {
      font-size: 80%;
      overflow: auto;
      background-color: #FFF;
    }

    .info {
      padding: 0.5em;
    }

    .source .data .line_numbers {
      background-color: #ECECEC;
      color: #555;
      padding: 1em .5em;
      border-right: 1px solid #DDD;
      text-align: right;
    }

    .line {
      padding-left: 10px;
      white-space: pre;
    }

    .line:hover {
      background-color: #F6F6F6;
    }

    .line.active {
      background-color: #FCC;
    }

    .error_highlight {
      display: inline-block;
      background-color: #FF9;
      text-decoration: #F00 wavy underline;
    }

    .error_highlight_tip {
      color: #666;
      padding: 2px 2px;
      font-size: 10px;
    }

    .button_to {
      display: inline-block;
      margin-top: 0.75em;
      margin-bottom: 0.75em;
    }

    .hidden {
      display: none;
    }

    .correction {
      list-style-type: none;
    }

    input[type="submit"] {
      color: white;
      background-color: #C00;
      border: none;
      border-radius: 12px;
      box-shadow: 0 3px #F99;
      font-size: 13px;
      font-weight: bold;
      margin: 0;
      padding: 10px 18px;
      cursor: pointer;
      -webkit-appearance: none;
    }
    input[type="submit"]:focus,
    input[type="submit"]:hover {
      opacity: 0.8;
    }
    input[type="submit"]:active {
      box-shadow: 0 2px #F99;
      transform: translateY(1px)
    }

    a { color: #980905; }
    a:visited { color: #666; }
    a.trace-frames {
      color: #666;
      overflow-wrap: break-word;
    }
    a:hover, a.trace-frames.selected { color: #C00; }
    a.summary:hover { color: #FFF; }

    @media (prefers-color-scheme: dark) {
      body {
        background-color: #222;
        color: #ECECEC;
      }

      .details, .summary {
        border-color: #666;
      }

      .source {
        border-color: #555;
        background-color: #333;
      }

      .source .data {
        background: #444;
      }

      .source .data .line_numbers {
        background: #333;
        border-color: #222;
      }

      .line:hover {
        background: #666;
      }

      .line.active {
        background-color: #900;
      }

      .error_highlight {
        color: #333;
      }

      input[type="submit"] {
        box-shadow: 0 3px #800;
      }
      input[type="submit"]:active {
        box-shadow: 0 2px #800;
      }

      a { color: #C00; }
      a.trace-frames { color: #999; }
      a:hover, a.trace-frames.selected { color: #E9382B; }
    }

    
  </style>

  <script>
    var toggle = function(id) {
      document.getElementById(id).classList.toggle('hidden');
      return false;
    }
    var show = function(id) {
      document.getElementById(id).style.display = 'block';
    }
    var hide = function(id) {
      document.getElementById(id).style.display = 'none';
    }
    var toggleSessionDump = function() {
      return toggle('session_dump');
    }
    var toggleEnvDump = function() {
      return toggle('env_dump');
    }
  </script>
<script src="/assets/hotwire-livereload-2d204a20.js" defer="defer"></script></head>
<body>

  <header>
  <h1>
    Dry::Struct::Error
      in Analytics::HubspotCompaniesController#show
  </h1>
</header>

<main role="main" id="container">
    <div class="exception-message">
    <div class="message">[Analytics::ShipperEvents::Shipper.new] nil (NilClass) has invalid type for :insight violates constraints (type?(Analytics::ShipperIntentInsight(id: integer, feed_id: integer, analytics_company_id: integer, status: string, queued_at: datetime, complete_at: datetime, created_at: datetime, updated_at: datetime, uuid: uuid), nil) failed)</div>
  </div>

  


  

  
<p><code>Rails.root: /Users/<USER>/github/carriersource/carrier_source</code></p>

<div id="traces-0">
    <a href="#" onclick="hide(&#39;Framework-Trace-0&#39;);hide(&#39;Full-Trace-0&#39;);show(&#39;Application-Trace-0&#39;);; return false;">Application Trace</a> |
    <a href="#" onclick="hide(&#39;Application-Trace-0&#39;);hide(&#39;Full-Trace-0&#39;);show(&#39;Framework-Trace-0&#39;);; return false;">Framework Trace</a> |
    <a href="#" onclick="hide(&#39;Application-Trace-0&#39;);hide(&#39;Framework-Trace-0&#39;);show(&#39;Full-Trace-0&#39;);; return false;">Full Trace</a> 

    <div id="Application-Trace-0" style="display: none;">
      <code class="traces">
      </code>
    </div>
    <div id="Framework-Trace-0" style="display: none;">
      <code class="traces">
      </code>
    </div>
    <div id="Full-Trace-0" style="display: block;">
      <code class="traces">
      </code>
    </div>

  <script>
    (function() {
      var traceFrames = document.getElementsByClassName('trace-frames-0');
      var selectedFrame, currentSource = document.getElementById('frame-source-0-0');

      // Add click listeners for all stack frames
      for (var i = 0; i < traceFrames.length; i++) {
        traceFrames[i].addEventListener('click', function(e) {
          e.preventDefault();
          var target = e.target;
          var frame_id = target.dataset.frameId;

          if (selectedFrame) {
            selectedFrame.className = selectedFrame.className.replace("selected", "");
          }

          target.className += " selected";
          selectedFrame = target;

          // Change the extracted source code
          changeSourceExtract(frame_id);
        });

        function changeSourceExtract(frame_id) {
          var el = document.getElementById('frame-source-0-' + frame_id);
          if (currentSource && el) {
            currentSource.className += " hidden";
            el.className = el.className.replace(" hidden", "");
            currentSource = el;
          }
        }
      }
    })();
  </script>
</div>


    <h2>Exception Causes</h2>

    <div class="details">
      <a class="summary" href="#" onclick="return toggle(253304)">
        Dry::Types::SchemaError: nil (NilClass) has invalid type for :insight violates constraints (type?(Analytics::ShipperIntentInsight(id: integer, feed_id: integer, analytics_company_id: integer, status: string, queued_at: datetime, complete_at: datetime, created_at: datetime, updated_at: datetime, uuid: uuid), nil) failed)
      </a>
    </div>

    <div id="253304" class="hidden">
      
    <div class="source hidden" id="frame-source-1-0">
      <div class="info">
        Extracted source (around line <strong>#330</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>328</span>
<span>329</span>
<span>330</span>
<span>331</span>
<span>332</span>
<span>333</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">              result[k] = type.call_unsafe(value)
</div><div class="line">            rescue ConstraintError =&gt; e
</div><div class="line active">              <span class="error_highlight">raise</span> SchemaError.new(type.name, value, e.result)
</div><div class="line">            rescue CoercionError =&gt; e
</div><div class="line">              raise SchemaError.new(type.name, value, e.message)
</div><div class="line">            end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-1">
      <div class="info">
        Extracted source (around line <strong>#322</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>320</span>
<span>321</span>
<span>322</span>
<span>323</span>
<span>324</span>
<span>325</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">        result = {}
</div><div class="line">
</div><div class="line active">        hash<span class="error_highlight">.each</span> do |key, value|
</div><div class="line">          k = @transform_key.(key)
</div><div class="line">          type = @name_key_map[k]
</div><div class="line">
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-2">
      <div class="info">
        Extracted source (around line <strong>#322</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>320</span>
<span>321</span>
<span>322</span>
<span>323</span>
<span>324</span>
<span>325</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">        result = {}
</div><div class="line">
</div><div class="line active">        hash<span class="error_highlight">.each</span> do |key, value|
</div><div class="line">          k = @transform_key.(key)
</div><div class="line">          type = @name_key_map[k]
</div><div class="line">
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-3">
      <div class="info">
        Extracted source (around line <strong>#60</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>58</span>
<span>59</span>
<span>60</span>
<span>61</span>
<span>62</span>
<span>63</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      # @api private
</div><div class="line">      def call_unsafe(hash, options = EMPTY_HASH)
</div><div class="line active">        <span class="error_highlight">resolve_unsafe</span>(coerce(hash), options)
</div><div class="line">      end
</div><div class="line">
</div><div class="line">      # @param [Hash] hash
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-4">
      <div class="info">
        Extracted source (around line <strong>#80</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>78</span>
<span>79</span>
<span>80</span>
<span>81</span>
<span>82</span>
<span>83</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      #
</div><div class="line">      # @api private
</div><div class="line active">      def call_unsafe(input) = type<span class="error_highlight">.call_unsafe</span>(fn.(input))
</div><div class="line">
</div><div class="line">      # @param [Object] input
</div><div class="line">      # @param [#call,nil] block
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-5">
      <div class="info">
        Extracted source (around line <strong>#254</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>252</span>
<span>253</span>
<span>254</span>
<span>255</span>
<span>256</span>
<span>257</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">          load(schema.call_safe(attributes) { |output = attributes| return yield output })
</div><div class="line">        else
</div><div class="line active">          load(schema<span class="error_highlight">.call_unsafe</span>(attributes))
</div><div class="line">        end
</div><div class="line">      rescue Types::CoercionError =&gt; e
</div><div class="line">        raise Error, &quot;[#{self}.new] #{e}&quot;, e.backtrace
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source " id="frame-source-1-6">
      <div class="info">
        Extracted source (around line <strong>#14</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>12</span>
<span>13</span>
<span>14</span>
<span>15</span>
<span>16</span>
<span>17</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">
</div><div class="line">      render json: Analytics::ShipperEvents::Blueprints::Shipper.render(
</div><div class="line active">        Analytics::ShipperEvents::Shipper<span class="error_highlight">.new</span>(intent:, insight:)
</div><div class="line">      )
</div><div class="line">    end
</div><div class="line">
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-7">
      <div class="info">
        Extracted source (around line <strong>#8</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>6</span>
<span>7</span>
<span>8</span>
<span>9</span>
<span>10</span>
<span>11</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">  module BasicImplicitRender # :nodoc:
</div><div class="line">    def send_action(method, *args)
</div><div class="line active">      ret = super
</div><div class="line">      default_render unless performed?
</div><div class="line">      ret
</div><div class="line">    end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-8">
      <div class="info">
        Extracted source (around line <strong>#215</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>213</span>
<span>214</span>
<span>215</span>
<span>216</span>
<span>217</span>
<span>218</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      # necessarily the same as the action name.
</div><div class="line">      def process_action(...)
</div><div class="line active">        <span class="error_highlight">send_action</span>(...)
</div><div class="line">      end
</div><div class="line">
</div><div class="line">      # Actually call the method associated with the action. Override this method if
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-9">
      <div class="info">
        Extracted source (around line <strong>#193</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>191</span>
<span>192</span>
<span>193</span>
<span>194</span>
<span>195</span>
<span>196</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      def process_action(*) # :nodoc:
</div><div class="line">        self.formats = request.formats.filter_map(&amp;:ref)
</div><div class="line active">        super
</div><div class="line">      end
</div><div class="line">
</div><div class="line">      def _process_variant(options)
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-10">
      <div class="info">
        Extracted source (around line <strong>#261</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>259</span>
<span>260</span>
<span>261</span>
<span>262</span>
<span>263</span>
<span>264</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      def process_action(...)
</div><div class="line">        run_callbacks(:process_action) do
</div><div class="line active">          super
</div><div class="line">        end
</div><div class="line">      end
</div><div class="line">  end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-11">
      <div class="info">
        Extracted source (around line <strong>#120</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>118</span>
<span>119</span>
<span>120</span>
<span>121</span>
<span>122</span>
<span>123</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">              current.invoke_before(env)
</div><div class="line">              if current.final?
</div><div class="line active">                env.value = !env.halted &amp;&amp; (!block_given? || yield)
</div><div class="line">              elsif current.skip?(env)
</div><div class="line">                (skipped ||= []) &lt;&lt; current
</div><div class="line">                next_sequence = next_sequence.nested
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-12">
      <div class="info">
        Extracted source (around line <strong>#16</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>14</span>
<span>15</span>
<span>16</span>
<span>17</span>
<span>18</span>
<span>19</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      self.controller = controller
</div><div class="line">      STORED_DATA.each { |k, m| store[k] = send(m) }
</div><div class="line active">      yield
</div><div class="line">    ensure
</div><div class="line">      self.controller = nil
</div><div class="line">      STORED_DATA.keys.each { |k| store.delete(k) }
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-13">
      <div class="info">
        Extracted source (around line <strong>#129</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>127</span>
<span>128</span>
<span>129</span>
<span>130</span>
<span>131</span>
<span>132</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">                begin
</div><div class="line">                  target, block, method, *arguments = current.expand_call_template(env, invoke_sequence)
</div><div class="line active">                  target<span class="error_highlight">.send</span>(method, *arguments, &amp;block)
</div><div class="line">                ensure
</div><div class="line">                  next_sequence = current
</div><div class="line">                end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-14">
      <div class="info">
        Extracted source (around line <strong>#24</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>22</span>
<span>23</span>
<span>24</span>
<span>25</span>
<span>26</span>
<span>27</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">    def with_request_id(request_id)
</div><div class="line">      old_request_id, self.current_request_id = self.current_request_id, request_id
</div><div class="line active">      yield
</div><div class="line">    ensure
</div><div class="line">      self.current_request_id = old_request_id
</div><div class="line">    end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-15">
      <div class="info">
        Extracted source (around line <strong>#10</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>8</span>
<span>9</span>
<span>10</span>
<span>11</span>
<span>12</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">  private
</div><div class="line">    def turbo_tracking_request_id(&amp;block)
</div><div class="line active">      Turbo<span class="error_highlight">.with_request_id</span>(request.headers[&quot;X-Turbo-Request-Id&quot;], &amp;block)
</div><div class="line">    end
</div><div class="line">end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-16">
      <div class="info">
        Extracted source (around line <strong>#129</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>127</span>
<span>128</span>
<span>129</span>
<span>130</span>
<span>131</span>
<span>132</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">                begin
</div><div class="line">                  target, block, method, *arguments = current.expand_call_template(env, invoke_sequence)
</div><div class="line active">                  target<span class="error_highlight">.send</span>(method, *arguments, &amp;block)
</div><div class="line">                ensure
</div><div class="line">                  next_sequence = current
</div><div class="line">                end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-17">
      <div class="info">
        Extracted source (around line <strong>#16</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>14</span>
<span>15</span>
<span>16</span>
<span>17</span>
<span>18</span>
<span>19</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      self.controller = controller
</div><div class="line">      STORED_DATA.each { |k, m| store[k] = send(m) }
</div><div class="line active">      yield
</div><div class="line">    ensure
</div><div class="line">      self.controller = nil
</div><div class="line">      STORED_DATA.keys.each { |k| store.delete(k) }
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-18">
      <div class="info">
        Extracted source (around line <strong>#129</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>127</span>
<span>128</span>
<span>129</span>
<span>130</span>
<span>131</span>
<span>132</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">                begin
</div><div class="line">                  target, block, method, *arguments = current.expand_call_template(env, invoke_sequence)
</div><div class="line active">                  target<span class="error_highlight">.send</span>(method, *arguments, &amp;block)
</div><div class="line">                ensure
</div><div class="line">                  next_sequence = current
</div><div class="line">                end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-19">
      <div class="info">
        Extracted source (around line <strong>#50</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>48</span>
<span>49</span>
<span>50</span>
<span>51</span>
<span>52</span>
<span>53</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      begin
</div><div class="line">        Ahoy.instance = ahoy
</div><div class="line active">        yield
</div><div class="line">      ensure
</div><div class="line">        Ahoy.instance = previous_value
</div><div class="line">      end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-20">
      <div class="info">
        Extracted source (around line <strong>#129</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>127</span>
<span>128</span>
<span>129</span>
<span>130</span>
<span>131</span>
<span>132</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">                begin
</div><div class="line">                  target, block, method, *arguments = current.expand_call_template(env, invoke_sequence)
</div><div class="line active">                  target<span class="error_highlight">.send</span>(method, *arguments, &amp;block)
</div><div class="line">                ensure
</div><div class="line">                  next_sequence = current
</div><div class="line">                end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-21">
      <div class="info">
        Extracted source (around line <strong>#140</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>138</span>
<span>139</span>
<span>140</span>
<span>141</span>
<span>142</span>
<span>143</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">          end
</div><div class="line">
</div><div class="line active">          invoke_sequence<span class="error_highlight">.call</span>
</div><div class="line">        end
</div><div class="line">      end
</div><div class="line">    end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-22">
      <div class="info">
        Extracted source (around line <strong>#260</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>258</span>
<span>259</span>
<span>260</span>
<span>261</span>
<span>262</span>
<span>263</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      # callbacks around the normal behavior.
</div><div class="line">      def process_action(...)
</div><div class="line active">        <span class="error_highlight">run_callbacks</span>(:process_action) do
</div><div class="line">          super
</div><div class="line">        end
</div><div class="line">      end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-23">
      <div class="info">
        Extracted source (around line <strong>#27</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>25</span>
<span>26</span>
<span>27</span>
<span>28</span>
<span>29</span>
<span>30</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">    private
</div><div class="line">      def process_action(*)
</div><div class="line active">        super
</div><div class="line">      rescue Exception =&gt; exception
</div><div class="line">        request.env[&quot;action_dispatch.show_detailed_exceptions&quot;] ||= show_detailed_exceptions?
</div><div class="line">        rescue_with_handler(exception) || raise
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-24">
      <div class="info">
        Extracted source (around line <strong>#76</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>74</span>
<span>75</span>
<span>76</span>
<span>77</span>
<span>78</span>
<span>79</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">
</div><div class="line">        ActiveSupport::Notifications.instrument(&quot;process_action.action_controller&quot;, raw_payload) do |payload|
</div><div class="line active">          result = super
</div><div class="line">          payload[:response] = response
</div><div class="line">          payload[:status]   = response.status
</div><div class="line">          result
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-25">
      <div class="info">
        Extracted source (around line <strong>#210</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>208</span>
<span>209</span>
<span>210</span>
<span>211</span>
<span>212</span>
<span>213</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      def instrument(name, payload = {})
</div><div class="line">        if notifier.listening?(name)
</div><div class="line active">          instrumenter.instrument(name, payload) { yield payload if block_given? }
</div><div class="line">        else
</div><div class="line">          yield payload if block_given?
</div><div class="line">        end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-26">
      <div class="info">
        Extracted source (around line <strong>#58</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>56</span>
<span>57</span>
<span>58</span>
<span>59</span>
<span>60</span>
<span>61</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">        handle.start
</div><div class="line">        begin
</div><div class="line active">          yield payload if block_given?
</div><div class="line">        rescue Exception =&gt; e
</div><div class="line">          payload[:exception] = [e.class.name, e.message]
</div><div class="line">          payload[:exception_object] = e
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-27">
      <div class="info">
        Extracted source (around line <strong>#210</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>208</span>
<span>209</span>
<span>210</span>
<span>211</span>
<span>212</span>
<span>213</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      def instrument(name, payload = {})
</div><div class="line">        if notifier.listening?(name)
</div><div class="line active">          instrumenter<span class="error_highlight">.instrument</span>(name, payload) { yield payload if block_given? }
</div><div class="line">        else
</div><div class="line">          yield payload if block_given?
</div><div class="line">        end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-28">
      <div class="info">
        Extracted source (around line <strong>#75</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>73</span>
<span>74</span>
<span>75</span>
<span>76</span>
<span>77</span>
<span>78</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">        ActiveSupport::Notifications.instrument(&quot;start_processing.action_controller&quot;, raw_payload)
</div><div class="line">
</div><div class="line active">        ActiveSupport::Notifications<span class="error_highlight">.instrument</span>(&quot;process_action.action_controller&quot;, raw_payload) do |payload|
</div><div class="line">          result = super
</div><div class="line">          payload[:response] = response
</div><div class="line">          payload[:status]   = response.status
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-29">
      <div class="info">
        Extracted source (around line <strong>#259</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>257</span>
<span>258</span>
<span>259</span>
<span>260</span>
<span>261</span>
<span>262</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      def process_action(*)
</div><div class="line">        _perform_parameter_wrapping if _wrapper_enabled?
</div><div class="line active">        super
</div><div class="line">      end
</div><div class="line">
</div><div class="line">      # Returns the wrapper key which will be used to store wrapped parameters.
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-30">
      <div class="info">
        Extracted source (around line <strong>#39</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>37</span>
<span>38</span>
<span>39</span>
<span>40</span>
<span>41</span>
<span>42</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">          # and it won&#39;t be cleaned up by the method below.
</div><div class="line">          ActiveRecord::RuntimeRegistry.reset
</div><div class="line active">          super
</div><div class="line">        end
</div><div class="line">
</div><div class="line">        def cleanup_view_runtime
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-31">
      <div class="info">
        Extracted source (around line <strong>#152</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>150</span>
<span>151</span>
<span>152</span>
<span>153</span>
<span>154</span>
<span>155</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      @_response_body = nil
</div><div class="line">
</div><div class="line active">      <span class="error_highlight">process_action</span>(action_name, ...)
</div><div class="line">    end
</div><div class="line">
</div><div class="line">    # Delegates to the class&#39;s ::controller_path.
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-32">
      <div class="info">
        Extracted source (around line <strong>#40</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>38</span>
<span>39</span>
<span>40</span>
<span>41</span>
<span>42</span>
<span>43</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">    def process(...) # :nodoc:
</div><div class="line">      old_config, I18n.config = I18n.config, I18nProxy.new(I18n.config, lookup_context)
</div><div class="line active">      super
</div><div class="line">    ensure
</div><div class="line">      I18n.config = old_config
</div><div class="line">    end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-33">
      <div class="info">
        Extracted source (around line <strong>#252</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>250</span>
<span>251</span>
<span>252</span>
<span>253</span>
<span>254</span>
<span>255</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      set_request!(request)
</div><div class="line">      set_response!(response)
</div><div class="line active">      <span class="error_highlight">process</span>(name)
</div><div class="line">      request.commit_flash
</div><div class="line">      to_a
</div><div class="line">    end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-34">
      <div class="info">
        Extracted source (around line <strong>#335</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>333</span>
<span>334</span>
<span>335</span>
<span>336</span>
<span>337</span>
<span>338</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">        middleware_stack.build(name) { |env| new.dispatch(name, req, res) }.call req.env
</div><div class="line">      else
</div><div class="line active">        new<span class="error_highlight">.dispatch</span>(name, req, res)
</div><div class="line">      end
</div><div class="line">    end
</div><div class="line">  end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-35">
      <div class="info">
        Extracted source (around line <strong>#67</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>65</span>
<span>66</span>
<span>67</span>
<span>68</span>
<span>69</span>
<span>70</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">
</div><div class="line">          def dispatch(controller, action, req, res)
</div><div class="line active">            controller<span class="error_highlight">.dispatch</span>(action, req, res)
</div><div class="line">          end
</div><div class="line">      end
</div><div class="line">
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-36">
      <div class="info">
        Extracted source (around line <strong>#50</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>48</span>
<span>49</span>
<span>50</span>
<span>51</span>
<span>52</span>
<span>53</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">          controller = controller req
</div><div class="line">          res        = controller.make_response! req
</div><div class="line active">          <span class="error_highlight">dispatch</span>(controller, params[:action], req, res)
</div><div class="line">        rescue ActionController::RoutingError
</div><div class="line">          if @raise_on_name_error
</div><div class="line">            raise
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-37">
      <div class="info">
        Extracted source (around line <strong>#53</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>51</span>
<span>52</span>
<span>53</span>
<span>54</span>
<span>55</span>
<span>56</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">          req.route_uri_pattern = route.path.spec.to_s
</div><div class="line">
</div><div class="line active">          _, headers, _ = response = route.app<span class="error_highlight">.serve</span>(req)
</div><div class="line">
</div><div class="line">          if &quot;pass&quot; == headers[Constants::X_CASCADE]
</div><div class="line">            req.script_name     = script_name
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-38">
      <div class="info">
        Extracted source (around line <strong>#133</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>131</span>
<span>132</span>
<span>133</span>
<span>134</span>
<span>135</span>
<span>136</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">              path_parameters[name.to_sym] = Utils.unescape_uri(val) if val
</div><div class="line">            }
</div><div class="line active">            yield [match_data, path_parameters, r]
</div><div class="line">          }
</div><div class="line">        end
</div><div class="line">
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-39">
      <div class="info">
        Extracted source (around line <strong>#126</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>124</span>
<span>125</span>
<span>126</span>
<span>127</span>
<span>128</span>
<span>129</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">          routes.sort_by!(&amp;:precedence)
</div><div class="line">
</div><div class="line active">          routes<span class="error_highlight">.each</span> { |r|
</div><div class="line">            match_data = r.path.match(path_info)
</div><div class="line">            path_parameters = {}
</div><div class="line">            match_data.names.each_with_index { |name, i|
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-40">
      <div class="info">
        Extracted source (around line <strong>#126</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>124</span>
<span>125</span>
<span>126</span>
<span>127</span>
<span>128</span>
<span>129</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">          routes.sort_by!(&amp;:precedence)
</div><div class="line">
</div><div class="line active">          routes<span class="error_highlight">.each</span> { |r|
</div><div class="line">            match_data = r.path.match(path_info)
</div><div class="line">            path_parameters = {}
</div><div class="line">            match_data.names.each_with_index { |name, i|
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-41">
      <div class="info">
        Extracted source (around line <strong>#34</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>32</span>
<span>33</span>
<span>34</span>
<span>35</span>
<span>36</span>
<span>37</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">
</div><div class="line">      def serve(req)
</div><div class="line active">        <span class="error_highlight">find_routes</span>(req) do |match, parameters, route|
</div><div class="line">          set_params  = req.path_parameters
</div><div class="line">          path_info   = req.path_info
</div><div class="line">          script_name = req.script_name
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-42">
      <div class="info">
        Extracted source (around line <strong>#908</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>906</span>
<span>907</span>
<span>908</span>
<span>909</span>
<span>910</span>
<span>911</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">        req = make_request(env)
</div><div class="line">        req.path_info = Journey::Router::Utils.normalize_path(req.path_info)
</div><div class="line active">        @router<span class="error_highlight">.serve</span>(req)
</div><div class="line">      end
</div><div class="line">
</div><div class="line">      def recognize_path(path, environment = {})
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-43">
      <div class="info">
        Extracted source (around line <strong>#202</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>200</span>
<span>201</span>
<span>202</span>
<span>203</span>
<span>204</span>
<span>205</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      end
</div><div class="line">
</div><div class="line active">      @app<span class="error_highlight">.call</span>(env)
</div><div class="line">    end
</div><div class="line">
</div><div class="line">    def warn_if_using_get_on_request_path
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-44">
      <div class="info">
        Extracted source (around line <strong>#169</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>167</span>
<span>168</span>
<span>169</span>
<span>170</span>
<span>171</span>
<span>172</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">    # @param [Hash] The Rack environment.
</div><div class="line">    def call(env)
</div><div class="line active">      dup<span class="error_highlight">.call!</span>(env)
</div><div class="line">    end
</div><div class="line">
</div><div class="line">    # The logic for dispatching any additional actions that need
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-45">
      <div class="info">
        Extracted source (around line <strong>#202</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>200</span>
<span>201</span>
<span>202</span>
<span>203</span>
<span>204</span>
<span>205</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      end
</div><div class="line">
</div><div class="line active">      @app<span class="error_highlight">.call</span>(env)
</div><div class="line">    end
</div><div class="line">
</div><div class="line">    def warn_if_using_get_on_request_path
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-46">
      <div class="info">
        Extracted source (around line <strong>#169</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>167</span>
<span>168</span>
<span>169</span>
<span>170</span>
<span>171</span>
<span>172</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">    # @param [Hash] The Rack environment.
</div><div class="line">    def call(env)
</div><div class="line active">      dup<span class="error_highlight">.call!</span>(env)
</div><div class="line">    end
</div><div class="line">
</div><div class="line">    # The logic for dispatching any additional actions that need
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-47">
      <div class="info">
        Extracted source (around line <strong>#202</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>200</span>
<span>201</span>
<span>202</span>
<span>203</span>
<span>204</span>
<span>205</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      end
</div><div class="line">
</div><div class="line active">      @app<span class="error_highlight">.call</span>(env)
</div><div class="line">    end
</div><div class="line">
</div><div class="line">    def warn_if_using_get_on_request_path
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-48">
      <div class="info">
        Extracted source (around line <strong>#169</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>167</span>
<span>168</span>
<span>169</span>
<span>170</span>
<span>171</span>
<span>172</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">    # @param [Hash] The Rack environment.
</div><div class="line">    def call(env)
</div><div class="line active">      dup<span class="error_highlight">.call!</span>(env)
</div><div class="line">    end
</div><div class="line">
</div><div class="line">    # The logic for dispatching any additional actions that need
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-49">
      <div class="info">
        Extracted source (around line <strong>#44</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>42</span>
<span>43</span>
<span>44</span>
<span>45</span>
<span>46</span>
<span>47</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">
</div><div class="line">    def call(env)
</div><div class="line active">      to_app<span class="error_highlight">.call</span>(env)
</div><div class="line">    end
</div><div class="line">  end
</div><div class="line">end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-50">
      <div class="info">
        Extracted source (around line <strong>#127</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>125</span>
<span>126</span>
<span>127</span>
<span>128</span>
<span>129</span>
<span>130</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      else
</div><div class="line">        configuration.tracked?(request)
</div><div class="line active">        @app<span class="error_highlight">.call</span>(env)
</div><div class="line">      end
</div><div class="line">    end
</div><div class="line">  end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-51">
      <div class="info">
        Extracted source (around line <strong>#16</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>14</span>
<span>15</span>
<span>16</span>
<span>17</span>
<span>18</span>
<span>19</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">        return [406, {}, []] unless valid_accept?(env[&#39;HTTP_ACCEPT&#39;])
</div><div class="line">
</div><div class="line active">        @app<span class="error_highlight">.call</span>(env)
</div><div class="line">      end
</div><div class="line">
</div><div class="line">      private
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-52">
      <div class="info">
        Extracted source (around line <strong>#11</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>9</span>
<span>10</span>
<span>11</span>
<span>12</span>
<span>13</span>
<span>14</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">    def call(env)
</div><div class="line">      sign_in_as_user(env)
</div><div class="line active">      app<span class="error_highlight">.call</span>(env)
</div><div class="line">    end
</div><div class="line">
</div><div class="line">    private
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-53">
      <div class="info">
        Extracted source (around line <strong>#14</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>12</span>
<span>13</span>
<span>14</span>
<span>15</span>
<span>16</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">    cookies[NAME] = { expires: 1.year.from_now, httponly: true, value: SecureRandom.uuid } unless cookies[NAME]
</div><div class="line">    env[NAME] = cookies[NAME]
</div><div class="line active">    app<span class="error_highlight">.call</span>(env)
</div><div class="line">  end
</div><div class="line">end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-54">
      <div class="info">
        Extracted source (around line <strong>#59</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>57</span>
<span>58</span>
<span>59</span>
<span>60</span>
<span>61</span>
<span>62</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      end
</div><div class="line">
</div><div class="line active">      status, headers, body = @app<span class="error_highlight">.call</span>(env)
</div><div class="line">
</div><div class="line">      if source != cookie_tag
</div><div class="line">        bake_cookies(headers, source, medium, term, content, campaign, from, time, lp)
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-55">
      <div class="info">
        Extracted source (around line <strong>#47</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>45</span>
<span>46</span>
<span>47</span>
<span>48</span>
<span>49</span>
<span>50</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">
</div><div class="line">    def call(env)
</div><div class="line active">      status, headers, body = response = @app<span class="error_highlight">.call</span>(env)
</div><div class="line">
</div><div class="line">      unless should_deflate?(env, status, headers, body)
</div><div class="line">        return response
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-56">
      <div class="info">
        Extracted source (around line <strong>#24</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>22</span>
<span>23</span>
<span>24</span>
<span>25</span>
<span>26</span>
<span>27</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">
</div><div class="line">      if redirect.canonical?
</div><div class="line active">        app<span class="error_highlight">.call</span>(env)
</div><div class="line">      else
</div><div class="line">        redirect.response
</div><div class="line">      end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-57">
      <div class="info">
        Extracted source (around line <strong>#162</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>160</span>
<span>161</span>
<span>162</span>
<span>163</span>
<span>164</span>
<span>165</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">        response
</div><div class="line">      else
</div><div class="line active">        @app<span class="error_highlight">.call</span>(env)
</div><div class="line">      end
</div><div class="line">    end
</div><div class="line">
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-58">
      <div class="info">
        Extracted source (around line <strong>#23</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>21</span>
<span>22</span>
<span>23</span>
<span>24</span>
<span>25</span>
<span>26</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      session = Clearance::Session.new(env)
</div><div class="line">      env[:clearance] = session
</div><div class="line active">      response = @app<span class="error_highlight">.call</span>(env)
</div><div class="line">
</div><div class="line">      if session.authentication_successful?
</div><div class="line">        session.add_cookie_to_headers
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-59">
      <div class="info">
        Extracted source (around line <strong>#13</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>11</span>
<span>12</span>
<span>13</span>
<span>14</span>
<span>15</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">    cookies = ::ActionDispatch::Request.new(env).cookie_jar
</div><div class="line">    cookies[NAME] = { expires: 1.year.from_now, httponly: true, value: &#39;true&#39; } unless cookies[NAME]
</div><div class="line active">    app<span class="error_highlight">.call</span>(env)
</div><div class="line">  end
</div><div class="line">end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-60">
      <div class="info">
        Extracted source (around line <strong>#20</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>18</span>
<span>19</span>
<span>20</span>
<span>21</span>
<span>22</span>
<span>23</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">
</div><div class="line">      begin
</div><div class="line active">        _, _, body = response = @app<span class="error_highlight">.call</span>(env)
</div><div class="line">      rescue Exception
</div><div class="line">        env[RACK_TEMPFILES]&amp;.each(&amp;:close!)
</div><div class="line">        raise
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-61">
      <div class="info">
        Extracted source (around line <strong>#29</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>27</span>
<span>28</span>
<span>29</span>
<span>30</span>
<span>31</span>
<span>32</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">
</div><div class="line">    def call(env)
</div><div class="line active">      status, headers, body = response = @app<span class="error_highlight">.call</span>(env)
</div><div class="line">
</div><div class="line">      if etag_status?(status) &amp;&amp; body.respond_to?(:to_ary) &amp;&amp; !skip_caching?(headers)
</div><div class="line">        body = body.to_ary
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-62">
      <div class="info">
        Extracted source (around line <strong>#31</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>29</span>
<span>30</span>
<span>31</span>
<span>32</span>
<span>33</span>
<span>34</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      case env[REQUEST_METHOD]
</div><div class="line">      when &quot;GET&quot;, &quot;HEAD&quot;
</div><div class="line active">        status, headers, body = response = @app<span class="error_highlight">.call</span>(env)
</div><div class="line">
</div><div class="line">        if status == 200 &amp;&amp; fresh?(env, headers)
</div><div class="line">          response[0] = 304
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-63">
      <div class="info">
        Extracted source (around line <strong>#15</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>13</span>
<span>14</span>
<span>15</span>
<span>16</span>
<span>17</span>
<span>18</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">
</div><div class="line">    def call(env)
</div><div class="line active">      _, _, body = response = @app<span class="error_highlight">.call</span>(env)
</div><div class="line">
</div><div class="line">      if env[REQUEST_METHOD] == HEAD
</div><div class="line">        body.close if body.respond_to?(:close)
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-64">
      <div class="info">
        Extracted source (around line <strong>#38</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>36</span>
<span>37</span>
<span>38</span>
<span>39</span>
<span>40</span>
<span>41</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">
</div><div class="line">      def call(env)
</div><div class="line active">        _, headers, _ = response = @app<span class="error_highlight">.call</span>(env)
</div><div class="line">
</div><div class="line">        return response if policy_present?(headers)
</div><div class="line">
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-65">
      <div class="info">
        Extracted source (around line <strong>#38</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>36</span>
<span>37</span>
<span>38</span>
<span>39</span>
<span>40</span>
<span>41</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">
</div><div class="line">      def call(env)
</div><div class="line active">        status, headers, _ = response = @app<span class="error_highlight">.call</span>(env)
</div><div class="line">
</div><div class="line">        # Returning CSP headers with a 304 Not Modified is harmful, since nonces in the
</div><div class="line">        # new CSP headers might not match nonces in the cached HTML.
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-66">
      <div class="info">
        Extracted source (around line <strong>#274</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>272</span>
<span>273</span>
<span>274</span>
<span>275</span>
<span>276</span>
<span>277</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">          req = make_request env
</div><div class="line">          prepare_session(req)
</div><div class="line active">          status, headers, body = app<span class="error_highlight">.call</span>(req.env)
</div><div class="line">          res = Rack::Response::Raw.new status, headers
</div><div class="line">          commit_session(req, res)
</div><div class="line">          [status, headers, body]
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-67">
      <div class="info">
        Extracted source (around line <strong>#268</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>266</span>
<span>267</span>
<span>268</span>
<span>269</span>
<span>270</span>
<span>271</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">
</div><div class="line">        def call(env)
</div><div class="line active">          <span class="error_highlight">context</span>(env)
</div><div class="line">        end
</div><div class="line">
</div><div class="line">        def context(env, app = @app)
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-68">
      <div class="info">
        Extracted source (around line <strong>#706</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>704</span>
<span>705</span>
<span>706</span>
<span>707</span>
<span>708</span>
<span>709</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">    def call(env)
</div><div class="line">      request = ActionDispatch::Request.new(env)
</div><div class="line active">      response = @app<span class="error_highlight">.call</span>(env)
</div><div class="line">
</div><div class="line">      if request.have_cookie_jar?
</div><div class="line">        cookie_jar = request.cookie_jar
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-69">
      <div class="info">
        Extracted source (around line <strong>#61</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>59</span>
<span>60</span>
<span>61</span>
<span>62</span>
<span>63</span>
<span>64</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">        # If cookies are not to be stripped, simply call the next middleware or application.
</div><div class="line">        # The original request and response headers remain untouched.
</div><div class="line active">        status, headers, body = @app<span class="error_highlight">.call</span>(env)
</div><div class="line">      end
</div><div class="line">
</div><div class="line">      # Return the final response to the client.
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-70">
      <div class="info">
        Extracted source (around line <strong>#670</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>668</span>
<span>669</span>
<span>670</span>
<span>671</span>
<span>672</span>
<span>673</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">        end
</div><div class="line">
</div><div class="line active">        @app<span class="error_highlight">.call</span>(env)
</div><div class="line">      end
</div><div class="line">
</div><div class="line">      private
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-71">
      <div class="info">
        Extracted source (around line <strong>#31</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>29</span>
<span>30</span>
<span>31</span>
<span>32</span>
<span>33</span>
<span>34</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      error = nil
</div><div class="line">      result = run_callbacks :call do
</div><div class="line active">        @app<span class="error_highlight">.call</span>(env)
</div><div class="line">      rescue =&gt; error
</div><div class="line">      end
</div><div class="line">      raise error if error
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-72">
      <div class="info">
        Extracted source (around line <strong>#100</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>98</span>
<span>99</span>
<span>100</span>
<span>101</span>
<span>102</span>
<span>103</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">
</div><div class="line">      if callbacks.empty?
</div><div class="line active">        yield if block_given?
</div><div class="line">      else
</div><div class="line">        env = Filters::Environment.new(self, false, nil)
</div><div class="line">
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-73">
      <div class="info">
        Extracted source (around line <strong>#30</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>28</span>
<span>29</span>
<span>30</span>
<span>31</span>
<span>32</span>
<span>33</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">    def call(env)
</div><div class="line">      error = nil
</div><div class="line active">      result = <span class="error_highlight">run_callbacks</span> :call do
</div><div class="line">        @app.call(env)
</div><div class="line">      rescue =&gt; error
</div><div class="line">      end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-74">
      <div class="info">
        Extracted source (around line <strong>#16</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>14</span>
<span>15</span>
<span>16</span>
<span>17</span>
<span>18</span>
<span>19</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      state = @executor.run!(reset: true)
</div><div class="line">      begin
</div><div class="line active">        response = @app<span class="error_highlight">.call</span>(env)
</div><div class="line">
</div><div class="line">        if env[&quot;action_dispatch.report_exception&quot;]
</div><div class="line">          error = env[&quot;action_dispatch.exception&quot;]
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-75">
      <div class="info">
        Extracted source (around line <strong>#18</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>16</span>
<span>17</span>
<span>18</span>
<span>19</span>
<span>20</span>
<span>21</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">    def call(env)
</div><div class="line">      request = ActionDispatch::Request.new(env)
</div><div class="line active">      return @app<span class="error_highlight">.call</span>(env) unless actionable_request?(request)
</div><div class="line">
</div><div class="line">      ActiveSupport::ActionableError.dispatch(request.params[:error].to_s.safe_constantize, request.params[:action])
</div><div class="line">
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-76">
      <div class="info">
        Extracted source (around line <strong>#31</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>29</span>
<span>30</span>
<span>31</span>
<span>32</span>
<span>33</span>
<span>34</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">
</div><div class="line">    def call(env)
</div><div class="line active">      _, headers, body = response = @app<span class="error_highlight">.call</span>(env)
</div><div class="line">
</div><div class="line">      if headers[Constants::X_CASCADE] == &quot;pass&quot;
</div><div class="line">        body.close if body.respond_to?(:close)
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-77">
      <div class="info">
        Extracted source (around line <strong>#32</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>30</span>
<span>31</span>
<span>32</span>
<span>33</span>
<span>34</span>
<span>35</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">
</div><div class="line">    def call(env)
</div><div class="line active">      @app<span class="error_highlight">.call</span>(env)
</div><div class="line">    rescue Exception =&gt; exception
</div><div class="line">      request = ActionDispatch::Request.new env
</div><div class="line">      backtrace_cleaner = request.get_header(&quot;action_dispatch.backtrace_cleaner&quot;)
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-78">
      <div class="info">
        Extracted source (around line <strong>#41</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>39</span>
<span>40</span>
<span>41</span>
<span>42</span>
<span>43</span>
<span>44</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">
</div><div class="line">          logger.info { started_request_message(request) }
</div><div class="line active">          status, headers, body = response = @app<span class="error_highlight">.call</span>(env)
</div><div class="line">          body = ::Rack::BodyProxy.new(body) { finish_request_instrumentation(handle, logger_tag_pop_count) }
</div><div class="line">
</div><div class="line">          if response.frozen?
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-79">
      <div class="info">
        Extracted source (around line <strong>#29</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>27</span>
<span>28</span>
<span>29</span>
<span>30</span>
<span>31</span>
<span>32</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">        end
</div><div class="line">
</div><div class="line active">        <span class="error_highlight">call_app</span>(request, env)
</div><div class="line">      end
</div><div class="line">
</div><div class="line">      private
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-80">
      <div class="info">
        Extracted source (around line <strong>#22</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>20</span>
<span>21</span>
<span>22</span>
<span>23</span>
<span>24</span>
<span>25</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">            end
</div><div class="line">          else
</div><div class="line active">            <span class="error_highlight">call_without_quiet_ahoy</span>(env)
</div><div class="line">          end
</div><div class="line">        end
</div><div class="line">        alias_method :call_without_quiet_ahoy, :call
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-81">
      <div class="info">
        Extracted source (around line <strong>#96</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>94</span>
<span>95</span>
<span>96</span>
<span>97</span>
<span>98</span>
<span>99</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      req = ActionDispatch::Request.new env
</div><div class="line">      req.remote_ip = GetIp.new(req, check_ip, proxies)
</div><div class="line active">      @app<span class="error_highlight">.call</span>(req.env)
</div><div class="line">    end
</div><div class="line">
</div><div class="line">    # The GetIp class exists as a way to defer processing of the request data into
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-82">
      <div class="info">
        Extracted source (around line <strong>#34</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>32</span>
<span>33</span>
<span>34</span>
<span>35</span>
<span>36</span>
<span>37</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      req = ActionDispatch::Request.new env
</div><div class="line">      req.request_id = make_request_id(req.get_header(@env_header))
</div><div class="line active">      @app<span class="error_highlight">.call</span>(env).tap { |_status, headers, _body| headers[@header] = req.request_id }
</div><div class="line">    end
</div><div class="line">
</div><div class="line">    private
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-83">
      <div class="info">
        Extracted source (around line <strong>#28</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>26</span>
<span>27</span>
<span>28</span>
<span>29</span>
<span>30</span>
<span>31</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      end
</div><div class="line">
</div><div class="line active">      @app<span class="error_highlight">.call</span>(env)
</div><div class="line">    end
</div><div class="line">
</div><div class="line">    def method_override(env)
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-84">
      <div class="info">
        Extracted source (around line <strong>#24</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>22</span>
<span>23</span>
<span>24</span>
<span>25</span>
<span>26</span>
<span>27</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">    def call(env)
</div><div class="line">      start_time = Utils.clock_time
</div><div class="line active">      _, headers, _ = response = @app<span class="error_highlight">.call</span>(env)
</div><div class="line">
</div><div class="line">      request_time = Utils.clock_time - start_time
</div><div class="line">
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-85">
      <div class="info">
        Extracted source (around line <strong>#29</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>27</span>
<span>28</span>
<span>29</span>
<span>30</span>
<span>31</span>
<span>32</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">          def call(env)
</div><div class="line">            LocalCacheRegistry.set_cache_for(local_cache_key, LocalStore.new)
</div><div class="line active">            response = @app<span class="error_highlight">.call</span>(env)
</div><div class="line">            response[2] = ::Rack::BodyProxy.new(response[2]) do
</div><div class="line">              LocalCacheRegistry.set_cache_for(local_cache_key, nil)
</div><div class="line">            end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-86">
      <div class="info">
        Extracted source (around line <strong>#61</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>59</span>
<span>60</span>
<span>61</span>
<span>62</span>
<span>63</span>
<span>64</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      response = nil
</div><div class="line">      events = @subscriber.collect_events do
</div><div class="line active">        response = @app<span class="error_highlight">.call</span>(env)
</div><div class="line">      end
</div><div class="line">
</div><div class="line">      headers = response[1]
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-87">
      <div class="info">
        Extracted source (around line <strong>#26</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>24</span>
<span>25</span>
<span>26</span>
<span>27</span>
<span>28</span>
<span>29</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">        events = []
</div><div class="line">        ActiveSupport::IsolatedExecutionState[KEY] = events
</div><div class="line active">        yield
</div><div class="line">        events
</div><div class="line">      ensure
</div><div class="line">        ActiveSupport::IsolatedExecutionState.delete(KEY)
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-88">
      <div class="info">
        Extracted source (around line <strong>#60</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>58</span>
<span>59</span>
<span>60</span>
<span>61</span>
<span>62</span>
<span>63</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">    def call(env)
</div><div class="line">      response = nil
</div><div class="line active">      events = @subscriber<span class="error_highlight">.collect_events</span> do
</div><div class="line">        response = @app.call(env)
</div><div class="line">      end
</div><div class="line">
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-89">
      <div class="info">
        Extracted source (around line <strong>#10</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>8</span>
<span>9</span>
<span>10</span>
<span>11</span>
<span>12</span>
<span>13</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      def call(env)
</div><div class="line">        request = ActionDispatch::Request.new(env)
</div><div class="line active">        status, headers, response = @app<span class="error_highlight">.call</span>(env)
</div><div class="line">
</div><div class="line">        if html_response?(headers)
</div><div class="line">          body = get_response_body(response)
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-90">
      <div class="info">
        Extracted source (around line <strong>#16</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>14</span>
<span>15</span>
<span>16</span>
<span>17</span>
<span>18</span>
<span>19</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      state = @executor.run!(reset: true)
</div><div class="line">      begin
</div><div class="line active">        response = @app<span class="error_highlight">.call</span>(env)
</div><div class="line">
</div><div class="line">        if env[&quot;action_dispatch.report_exception&quot;]
</div><div class="line">          error = env[&quot;action_dispatch.exception&quot;]
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-91">
      <div class="info">
        Extracted source (around line <strong>#37</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>35</span>
<span>36</span>
<span>37</span>
<span>38</span>
<span>39</span>
<span>40</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      end
</div><div class="line">    else
</div><div class="line active">      @app<span class="error_highlight">.call</span>(env)
</div><div class="line">    end
</div><div class="line">  end
</div><div class="line">
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-92">
      <div class="info">
        Extracted source (around line <strong>#27</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>25</span>
<span>26</span>
<span>27</span>
<span>28</span>
<span>29</span>
<span>30</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">
</div><div class="line">    def call(env)
</div><div class="line active">      @file_handler.attempt(env) || @app<span class="error_highlight">.call</span>(env)
</div><div class="line">    end
</div><div class="line">  end
</div><div class="line">
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-93">
      <div class="info">
        Extracted source (around line <strong>#114</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>112</span>
<span>113</span>
<span>114</span>
<span>115</span>
<span>116</span>
<span>117</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">
</div><div class="line">    def call(env)
</div><div class="line active">      _, headers, body = response = @app<span class="error_highlight">.call</span>(env)
</div><div class="line">
</div><div class="line">      if body.respond_to?(:to_path)
</div><div class="line">        case type = variation(env)
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-94">
      <div class="info">
        Extracted source (around line <strong>#143</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>141</span>
<span>142</span>
<span>143</span>
<span>144</span>
<span>145</span>
<span>146</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      if hosts.empty? || excluded?(request)
</div><div class="line">        mark_as_authorized(request)
</div><div class="line active">        @app<span class="error_highlight">.call</span>(env)
</div><div class="line">      else
</div><div class="line">        env[&quot;action_dispatch.blocked_hosts&quot;] = hosts
</div><div class="line">        @response_app.call(env)
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-95">
      <div class="info">
        Extracted source (around line <strong>#19</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>17</span>
<span>18</span>
<span>19</span>
<span>20</span>
<span>21</span>
<span>22</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">
</div><div class="line">    def call
</div><div class="line active">      return app<span class="error_highlight">.call</span>(env) if rule.nil?
</div><div class="line">      return proxy_with_newrelic if new_relic?
</div><div class="line">      proxy
</div><div class="line">    end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-96">
      <div class="info">
        Extracted source (around line <strong>#27</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>25</span>
<span>26</span>
<span>27</span>
<span>28</span>
<span>29</span>
<span>30</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">
</div><div class="line">    def call(env)
</div><div class="line active">      RoundTrip.new(@app, env, @global_options, @rules)<span class="error_highlight">.call</span>
</div><div class="line">    end
</div><div class="line">
</div><div class="line">    private
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-97">
      <div class="info">
        Extracted source (around line <strong>#102</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>100</span>
<span>101</span>
<span>102</span>
<span>103</span>
<span>104</span>
<span>105</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      vary_resource = resource_for_path(path)
</div><div class="line">
</div><div class="line active">      status, headers, body = @app<span class="error_highlight">.call</span> env
</div><div class="line">
</div><div class="line">      if add_headers
</div><div class="line">        headers = add_headers.merge(headers)
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-98">
      <div class="info">
        Extracted source (around line <strong>#535</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>533</span>
<span>534</span>
<span>535</span>
<span>536</span>
<span>537</span>
<span>538</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">    def call(env)
</div><div class="line">      req = build_request env
</div><div class="line active">      app<span class="error_highlight">.call</span> req.env
</div><div class="line">    end
</div><div class="line">
</div><div class="line">    # Defines additional Rack env configuration that is added on each call.
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-99">
      <div class="info">
        Extracted source (around line <strong>#299</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>297</span>
<span>298</span>
<span>299</span>
<span>300</span>
<span>301</span>
<span>302</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      def call(env)
</div><div class="line">        env[Const::PUMA_CONFIG] = @config
</div><div class="line active">        @app<span class="error_highlight">.call</span>(env)
</div><div class="line">      end
</div><div class="line">    end
</div><div class="line">
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-100">
      <div class="info">
        Extracted source (around line <strong>#101</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>99</span>
<span>100</span>
<span>101</span>
<span>102</span>
<span>103</span>
<span>104</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">        if @supported_http_methods == :any || @supported_http_methods.key?(env[REQUEST_METHOD])
</div><div class="line">          status, headers, app_body = @thread_pool.with_force_shutdown do
</div><div class="line active">            @app<span class="error_highlight">.call</span>(env)
</div><div class="line">          end
</div><div class="line">        else
</div><div class="line">          @log_writer.log &quot;Unsupported HTTP method used: #{env[REQUEST_METHOD]}&quot;
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-101">
      <div class="info">
        Extracted source (around line <strong>#346</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>344</span>
<span>345</span>
<span>346</span>
<span>347</span>
<span>348</span>
<span>349</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">        t[:with_force_shutdown] = true
</div><div class="line">      end
</div><div class="line active">      yield
</div><div class="line">    ensure
</div><div class="line">      t[:with_force_shutdown] = false
</div><div class="line">    end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-102">
      <div class="info">
        Extracted source (around line <strong>#100</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>98</span>
<span>99</span>
<span>100</span>
<span>101</span>
<span>102</span>
<span>103</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      begin
</div><div class="line">        if @supported_http_methods == :any || @supported_http_methods.key?(env[REQUEST_METHOD])
</div><div class="line active">          status, headers, app_body = @thread_pool<span class="error_highlight">.with_force_shutdown</span> do
</div><div class="line">            @app.call(env)
</div><div class="line">          end
</div><div class="line">        else
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-103">
      <div class="info">
        Extracted source (around line <strong>#506</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>504</span>
<span>505</span>
<span>506</span>
<span>507</span>
<span>508</span>
<span>509</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">
</div><div class="line">        @requests_count += 1
</div><div class="line active">        case <span class="error_highlight">handle_request</span>(client, requests + 1)
</div><div class="line">        when false
</div><div class="line">        when :async
</div><div class="line">          close_socket = false
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-104">
      <div class="info">
        Extracted source (around line <strong>#265</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>263</span>
<span>264</span>
<span>265</span>
<span>266</span>
<span>267</span>
<span>268</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      @status = :run
</div><div class="line">
</div><div class="line active">      @thread_pool = ThreadPool.new(thread_name, options) { |client| <span class="error_highlight">process_client</span> client }
</div><div class="line">
</div><div class="line">      if @queue_requests
</div><div class="line">        @reactor = Reactor.new(@io_selector_backend) { |c|
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-1-105">
      <div class="info">
        Extracted source (around line <strong>#173</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>171</span>
<span>172</span>
<span>173</span>
<span>174</span>
<span>175</span>
<span>176</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">
</div><div class="line">          begin
</div><div class="line active">            @out_of_band_pending = true if block<span class="error_highlight">.call</span>(work)
</div><div class="line">          rescue Exception =&gt; e
</div><div class="line">            STDERR.puts &quot;Error reached top of thread-pool: #{e.message} (#{e.class})&quot;
</div><div class="line">          end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>

      
<p><code>Rails.root: /Users/<USER>/github/carriersource/carrier_source</code></p>

<div id="traces-1">
    <a href="#" onclick="hide(&#39;Framework-Trace-1&#39;);hide(&#39;Full-Trace-1&#39;);show(&#39;Application-Trace-1&#39;);; return false;">Application Trace</a> |
    <a href="#" onclick="hide(&#39;Application-Trace-1&#39;);hide(&#39;Full-Trace-1&#39;);show(&#39;Framework-Trace-1&#39;);; return false;">Framework Trace</a> |
    <a href="#" onclick="hide(&#39;Application-Trace-1&#39;);hide(&#39;Framework-Trace-1&#39;);show(&#39;Full-Trace-1&#39;);; return false;">Full Trace</a> 

    <div id="Application-Trace-1" style="display: block;">
      <code class="traces">
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="6" href="#">
            app/controllers/analytics/hubspot_companies_controller.rb:14:in &#39;Analytics::HubspotCompaniesController#show&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="52" href="#">
            lib/clearance/impersonation.rb:11:in &#39;Clearance::Impersonation#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="53" href="#">
            lib/unique_session_cookie.rb:14:in &#39;UniqueSessionCookie#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="59" href="#">
            lib/cookie_consent.rb:13:in &#39;CookieConsent#call&#39;
          </a>
          <br>
      </code>
    </div>
    <div id="Framework-Trace-1" style="display: none;">
      <code class="traces">
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="0" href="#">
            dry-types (1.8.3) lib/dry/types/schema.rb:330:in &#39;block in Dry::Types::Schema#resolve_unsafe&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="1" href="#">
            dry-types (1.8.3) lib/dry/types/schema.rb:322:in &#39;Hash#each&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="2" href="#">
            dry-types (1.8.3) lib/dry/types/schema.rb:322:in &#39;Dry::Types::Schema#resolve_unsafe&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="3" href="#">
            dry-types (1.8.3) lib/dry/types/schema.rb:60:in &#39;Dry::Types::Schema#call_unsafe&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="4" href="#">
            dry-types (1.8.3) lib/dry/types/constructor.rb:80:in &#39;Dry::Types::Constructor#call_unsafe&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="5" href="#">
            dry-struct (1.8.0) lib/dry/struct/class_interface.rb:254:in &#39;Dry::Struct::ClassInterface#new&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="7" href="#">
            actionpack (8.0.3) lib/action_controller/metal/basic_implicit_render.rb:8:in &#39;ActionController::BasicImplicitRender#send_action&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="8" href="#">
            actionpack (8.0.3) lib/abstract_controller/base.rb:215:in &#39;AbstractController::Base#process_action&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="9" href="#">
            actionpack (8.0.3) lib/action_controller/metal/rendering.rb:193:in &#39;ActionController::Rendering#process_action&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="10" href="#">
            actionpack (8.0.3) lib/abstract_controller/callbacks.rb:261:in &#39;block in AbstractController::Callbacks#process_action&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="11" href="#">
            activesupport (8.0.3) lib/active_support/callbacks.rb:120:in &#39;block in ActiveSupport::Callbacks#run_callbacks&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="12" href="#">
            audited (5.8.0) lib/audited/sweeper.rb:16:in &#39;Audited::Sweeper#around&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="13" href="#">
            activesupport (8.0.3) lib/active_support/callbacks.rb:129:in &#39;block in ActiveSupport::Callbacks#run_callbacks&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="14" href="#">
            turbo-rails (2.0.16) lib/turbo-rails.rb:24:in &#39;Turbo.with_request_id&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="15" href="#">
            turbo-rails (2.0.16) app/controllers/concerns/turbo/request_id_tracking.rb:10:in &#39;Turbo::RequestIdTracking#turbo_tracking_request_id&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="16" href="#">
            activesupport (8.0.3) lib/active_support/callbacks.rb:129:in &#39;block in ActiveSupport::Callbacks#run_callbacks&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="17" href="#">
            audited (5.8.0) lib/audited/sweeper.rb:16:in &#39;Audited::Sweeper#around&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="18" href="#">
            activesupport (8.0.3) lib/active_support/callbacks.rb:129:in &#39;block in ActiveSupport::Callbacks#run_callbacks&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="19" href="#">
            ahoy (5d10839c3997) lib/ahoy/controller.rb:50:in &#39;Ahoy::Controller#set_ahoy_request_store&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="20" href="#">
            activesupport (8.0.3) lib/active_support/callbacks.rb:129:in &#39;block in ActiveSupport::Callbacks#run_callbacks&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="21" href="#">
            activesupport (8.0.3) lib/active_support/callbacks.rb:140:in &#39;ActiveSupport::Callbacks#run_callbacks&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="22" href="#">
            actionpack (8.0.3) lib/abstract_controller/callbacks.rb:260:in &#39;AbstractController::Callbacks#process_action&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="23" href="#">
            actionpack (8.0.3) lib/action_controller/metal/rescue.rb:27:in &#39;ActionController::Rescue#process_action&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="24" href="#">
            actionpack (8.0.3) lib/action_controller/metal/instrumentation.rb:76:in &#39;block in ActionController::Instrumentation#process_action&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="25" href="#">
            activesupport (8.0.3) lib/active_support/notifications.rb:210:in &#39;block in ActiveSupport::Notifications.instrument&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="26" href="#">
            activesupport (8.0.3) lib/active_support/notifications/instrumenter.rb:58:in &#39;ActiveSupport::Notifications::Instrumenter#instrument&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="27" href="#">
            activesupport (8.0.3) lib/active_support/notifications.rb:210:in &#39;ActiveSupport::Notifications.instrument&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="28" href="#">
            actionpack (8.0.3) lib/action_controller/metal/instrumentation.rb:75:in &#39;ActionController::Instrumentation#process_action&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="29" href="#">
            actionpack (8.0.3) lib/action_controller/metal/params_wrapper.rb:259:in &#39;ActionController::ParamsWrapper#process_action&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="30" href="#">
            activerecord (8.0.3) lib/active_record/railties/controller_runtime.rb:39:in &#39;ActiveRecord::Railties::ControllerRuntime#process_action&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="31" href="#">
            actionpack (8.0.3) lib/abstract_controller/base.rb:152:in &#39;AbstractController::Base#process&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="32" href="#">
            actionview (8.0.3) lib/action_view/rendering.rb:40:in &#39;ActionView::Rendering#process&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="33" href="#">
            actionpack (8.0.3) lib/action_controller/metal.rb:252:in &#39;ActionController::Metal#dispatch&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="34" href="#">
            actionpack (8.0.3) lib/action_controller/metal.rb:335:in &#39;ActionController::Metal.dispatch&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="35" href="#">
            actionpack (8.0.3) lib/action_dispatch/routing/route_set.rb:67:in &#39;ActionDispatch::Routing::RouteSet::Dispatcher#dispatch&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="36" href="#">
            actionpack (8.0.3) lib/action_dispatch/routing/route_set.rb:50:in &#39;ActionDispatch::Routing::RouteSet::Dispatcher#serve&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="37" href="#">
            actionpack (8.0.3) lib/action_dispatch/journey/router.rb:53:in &#39;block in ActionDispatch::Journey::Router#serve&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="38" href="#">
            actionpack (8.0.3) lib/action_dispatch/journey/router.rb:133:in &#39;block in ActionDispatch::Journey::Router#find_routes&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="39" href="#">
            actionpack (8.0.3) lib/action_dispatch/journey/router.rb:126:in &#39;Array#each&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="40" href="#">
            actionpack (8.0.3) lib/action_dispatch/journey/router.rb:126:in &#39;ActionDispatch::Journey::Router#find_routes&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="41" href="#">
            actionpack (8.0.3) lib/action_dispatch/journey/router.rb:34:in &#39;ActionDispatch::Journey::Router#serve&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="42" href="#">
            actionpack (8.0.3) lib/action_dispatch/routing/route_set.rb:908:in &#39;ActionDispatch::Routing::RouteSet#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="43" href="#">
            omniauth (2.1.3) lib/omniauth/strategy.rb:202:in &#39;OmniAuth::Strategy#call!&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="44" href="#">
            omniauth (2.1.3) lib/omniauth/strategy.rb:169:in &#39;OmniAuth::Strategy#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="45" href="#">
            omniauth (2.1.3) lib/omniauth/strategy.rb:202:in &#39;OmniAuth::Strategy#call!&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="46" href="#">
            omniauth (2.1.3) lib/omniauth/strategy.rb:169:in &#39;OmniAuth::Strategy#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="47" href="#">
            omniauth (2.1.3) lib/omniauth/strategy.rb:202:in &#39;OmniAuth::Strategy#call!&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="48" href="#">
            omniauth (2.1.3) lib/omniauth/strategy.rb:169:in &#39;OmniAuth::Strategy#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="49" href="#">
            omniauth (2.1.3) lib/omniauth/builder.rb:44:in &#39;OmniAuth::Builder#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="50" href="#">
            rack-attack (6.7.0) lib/rack/attack.rb:127:in &#39;Rack::Attack#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="51" href="#">
            jsonapi-rails (0.4.1) lib/jsonapi/rails/filter_media_type.rb:16:in &#39;JSONAPI::Rails::FilterMediaType#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="54" href="#">
            rack-utm (0.0.2) lib/rack-utm.rb:59:in &#39;Rack::Utm#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="55" href="#">
            rack (3.2.1) lib/rack/deflater.rb:47:in &#39;Rack::Deflater#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="56" href="#">
            rack-canonical-host (1.3.0) lib/rack/canonical_host.rb:24:in &#39;Rack::CanonicalHost#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="57" href="#">
            rack (3.2.1) lib/rack/static.rb:162:in &#39;Rack::Static#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="58" href="#">
            clearance (2.10.0) lib/clearance/rack_session.rb:23:in &#39;Clearance::RackSession#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="60" href="#">
            rack (3.2.1) lib/rack/tempfile_reaper.rb:20:in &#39;Rack::TempfileReaper#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="61" href="#">
            rack (3.2.1) lib/rack/etag.rb:29:in &#39;Rack::ETag#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="62" href="#">
            rack (3.2.1) lib/rack/conditional_get.rb:31:in &#39;Rack::ConditionalGet#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="63" href="#">
            rack (3.2.1) lib/rack/head.rb:15:in &#39;Rack::Head#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="64" href="#">
            actionpack (8.0.3) lib/action_dispatch/http/permissions_policy.rb:38:in &#39;ActionDispatch::PermissionsPolicy::Middleware#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="65" href="#">
            actionpack (8.0.3) lib/action_dispatch/http/content_security_policy.rb:38:in &#39;ActionDispatch::ContentSecurityPolicy::Middleware#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="66" href="#">
            rack-session (2.1.1) lib/rack/session/abstract/id.rb:274:in &#39;Rack::Session::Abstract::Persisted#context&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="67" href="#">
            rack-session (2.1.1) lib/rack/session/abstract/id.rb:268:in &#39;Rack::Session::Abstract::Persisted#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="68" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/cookies.rb:706:in &#39;ActionDispatch::Cookies#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="69" href="#">
            rack-strip-cookies (2.0.0) lib/rack/strip-cookies.rb:61:in &#39;Rack::StripCookies#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="70" href="#">
            activerecord (8.0.3) lib/active_record/migration.rb:670:in &#39;ActiveRecord::Migration::CheckPending#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="71" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/callbacks.rb:31:in &#39;block in ActionDispatch::Callbacks#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="72" href="#">
            activesupport (8.0.3) lib/active_support/callbacks.rb:100:in &#39;ActiveSupport::Callbacks#run_callbacks&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="73" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/callbacks.rb:30:in &#39;ActionDispatch::Callbacks#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="74" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/executor.rb:16:in &#39;ActionDispatch::Executor#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="75" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/actionable_exceptions.rb:18:in &#39;ActionDispatch::ActionableExceptions#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="76" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/debug_exceptions.rb:31:in &#39;ActionDispatch::DebugExceptions#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="77" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/show_exceptions.rb:32:in &#39;ActionDispatch::ShowExceptions#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="78" href="#">
            railties (8.0.3) lib/rails/rack/logger.rb:41:in &#39;Rails::Rack::Logger#call_app&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="79" href="#">
            railties (8.0.3) lib/rails/rack/logger.rb:29:in &#39;Rails::Rack::Logger#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="80" href="#">
            ahoy (5d10839c3997) lib/ahoy/engine.rb:22:in &#39;Rails::Rack::Logger#call_with_quiet_ahoy&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="81" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/remote_ip.rb:96:in &#39;ActionDispatch::RemoteIp#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="82" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/request_id.rb:34:in &#39;ActionDispatch::RequestId#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="83" href="#">
            rack (3.2.1) lib/rack/method_override.rb:28:in &#39;Rack::MethodOverride#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="84" href="#">
            rack (3.2.1) lib/rack/runtime.rb:24:in &#39;Rack::Runtime#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="85" href="#">
            activesupport (8.0.3) lib/active_support/cache/strategy/local_cache_middleware.rb:29:in &#39;ActiveSupport::Cache::Strategy::LocalCache::Middleware#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="86" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/server_timing.rb:61:in &#39;block in ActionDispatch::ServerTiming#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="87" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/server_timing.rb:26:in &#39;ActionDispatch::ServerTiming::Subscriber#collect_events&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="88" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/server_timing.rb:60:in &#39;ActionDispatch::ServerTiming#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="89" href="#">
            hotwire-livereload (2.0.0) lib/hotwire/livereload/middleware.rb:10:in &#39;Hotwire::Livereload::Middleware#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="90" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/executor.rb:16:in &#39;ActionDispatch::Executor#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="91" href="#">
            propshaft (1.3.1) lib/propshaft/server.rb:37:in &#39;Propshaft::Server#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="92" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/static.rb:27:in &#39;ActionDispatch::Static#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="93" href="#">
            rack (3.2.1) lib/rack/sendfile.rb:114:in &#39;Rack::Sendfile#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="94" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/host_authorization.rb:143:in &#39;ActionDispatch::HostAuthorization#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="95" href="#">
            rack-reverse-proxy (06f21feb6afb) lib/rack_reverse_proxy/roundtrip.rb:19:in &#39;RackReverseProxy::RoundTrip#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="96" href="#">
            rack-reverse-proxy (06f21feb6afb) lib/rack_reverse_proxy/middleware.rb:27:in &#39;RackReverseProxy::Middleware#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="97" href="#">
            rack-cors (3.0.0) lib/rack/cors.rb:102:in &#39;Rack::Cors#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="98" href="#">
            railties (8.0.3) lib/rails/engine.rb:535:in &#39;Rails::Engine#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="99" href="#">
            puma (7.0.4) lib/puma/configuration.rb:299:in &#39;Puma::Configuration::ConfigMiddleware#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="100" href="#">
            puma (7.0.4) lib/puma/request.rb:101:in &#39;block in Puma::Request#handle_request&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="101" href="#">
            puma (7.0.4) lib/puma/thread_pool.rb:346:in &#39;Puma::ThreadPool#with_force_shutdown&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="102" href="#">
            puma (7.0.4) lib/puma/request.rb:100:in &#39;Puma::Request#handle_request&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="103" href="#">
            puma (7.0.4) lib/puma/server.rb:506:in &#39;Puma::Server#process_client&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="104" href="#">
            puma (7.0.4) lib/puma/server.rb:265:in &#39;block in Puma::Server#run&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="105" href="#">
            puma (7.0.4) lib/puma/thread_pool.rb:173:in &#39;block in Puma::ThreadPool#spawn_thread&#39;
          </a>
          <br>
      </code>
    </div>
    <div id="Full-Trace-1" style="display: none;">
      <code class="traces">
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="0" href="#">
            dry-types (1.8.3) lib/dry/types/schema.rb:330:in &#39;block in Dry::Types::Schema#resolve_unsafe&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="1" href="#">
            dry-types (1.8.3) lib/dry/types/schema.rb:322:in &#39;Hash#each&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="2" href="#">
            dry-types (1.8.3) lib/dry/types/schema.rb:322:in &#39;Dry::Types::Schema#resolve_unsafe&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="3" href="#">
            dry-types (1.8.3) lib/dry/types/schema.rb:60:in &#39;Dry::Types::Schema#call_unsafe&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="4" href="#">
            dry-types (1.8.3) lib/dry/types/constructor.rb:80:in &#39;Dry::Types::Constructor#call_unsafe&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="5" href="#">
            dry-struct (1.8.0) lib/dry/struct/class_interface.rb:254:in &#39;Dry::Struct::ClassInterface#new&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="6" href="#">
            app/controllers/analytics/hubspot_companies_controller.rb:14:in &#39;Analytics::HubspotCompaniesController#show&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="7" href="#">
            actionpack (8.0.3) lib/action_controller/metal/basic_implicit_render.rb:8:in &#39;ActionController::BasicImplicitRender#send_action&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="8" href="#">
            actionpack (8.0.3) lib/abstract_controller/base.rb:215:in &#39;AbstractController::Base#process_action&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="9" href="#">
            actionpack (8.0.3) lib/action_controller/metal/rendering.rb:193:in &#39;ActionController::Rendering#process_action&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="10" href="#">
            actionpack (8.0.3) lib/abstract_controller/callbacks.rb:261:in &#39;block in AbstractController::Callbacks#process_action&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="11" href="#">
            activesupport (8.0.3) lib/active_support/callbacks.rb:120:in &#39;block in ActiveSupport::Callbacks#run_callbacks&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="12" href="#">
            audited (5.8.0) lib/audited/sweeper.rb:16:in &#39;Audited::Sweeper#around&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="13" href="#">
            activesupport (8.0.3) lib/active_support/callbacks.rb:129:in &#39;block in ActiveSupport::Callbacks#run_callbacks&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="14" href="#">
            turbo-rails (2.0.16) lib/turbo-rails.rb:24:in &#39;Turbo.with_request_id&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="15" href="#">
            turbo-rails (2.0.16) app/controllers/concerns/turbo/request_id_tracking.rb:10:in &#39;Turbo::RequestIdTracking#turbo_tracking_request_id&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="16" href="#">
            activesupport (8.0.3) lib/active_support/callbacks.rb:129:in &#39;block in ActiveSupport::Callbacks#run_callbacks&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="17" href="#">
            audited (5.8.0) lib/audited/sweeper.rb:16:in &#39;Audited::Sweeper#around&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="18" href="#">
            activesupport (8.0.3) lib/active_support/callbacks.rb:129:in &#39;block in ActiveSupport::Callbacks#run_callbacks&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="19" href="#">
            ahoy (5d10839c3997) lib/ahoy/controller.rb:50:in &#39;Ahoy::Controller#set_ahoy_request_store&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="20" href="#">
            activesupport (8.0.3) lib/active_support/callbacks.rb:129:in &#39;block in ActiveSupport::Callbacks#run_callbacks&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="21" href="#">
            activesupport (8.0.3) lib/active_support/callbacks.rb:140:in &#39;ActiveSupport::Callbacks#run_callbacks&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="22" href="#">
            actionpack (8.0.3) lib/abstract_controller/callbacks.rb:260:in &#39;AbstractController::Callbacks#process_action&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="23" href="#">
            actionpack (8.0.3) lib/action_controller/metal/rescue.rb:27:in &#39;ActionController::Rescue#process_action&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="24" href="#">
            actionpack (8.0.3) lib/action_controller/metal/instrumentation.rb:76:in &#39;block in ActionController::Instrumentation#process_action&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="25" href="#">
            activesupport (8.0.3) lib/active_support/notifications.rb:210:in &#39;block in ActiveSupport::Notifications.instrument&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="26" href="#">
            activesupport (8.0.3) lib/active_support/notifications/instrumenter.rb:58:in &#39;ActiveSupport::Notifications::Instrumenter#instrument&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="27" href="#">
            activesupport (8.0.3) lib/active_support/notifications.rb:210:in &#39;ActiveSupport::Notifications.instrument&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="28" href="#">
            actionpack (8.0.3) lib/action_controller/metal/instrumentation.rb:75:in &#39;ActionController::Instrumentation#process_action&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="29" href="#">
            actionpack (8.0.3) lib/action_controller/metal/params_wrapper.rb:259:in &#39;ActionController::ParamsWrapper#process_action&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="30" href="#">
            activerecord (8.0.3) lib/active_record/railties/controller_runtime.rb:39:in &#39;ActiveRecord::Railties::ControllerRuntime#process_action&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="31" href="#">
            actionpack (8.0.3) lib/abstract_controller/base.rb:152:in &#39;AbstractController::Base#process&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="32" href="#">
            actionview (8.0.3) lib/action_view/rendering.rb:40:in &#39;ActionView::Rendering#process&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="33" href="#">
            actionpack (8.0.3) lib/action_controller/metal.rb:252:in &#39;ActionController::Metal#dispatch&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="34" href="#">
            actionpack (8.0.3) lib/action_controller/metal.rb:335:in &#39;ActionController::Metal.dispatch&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="35" href="#">
            actionpack (8.0.3) lib/action_dispatch/routing/route_set.rb:67:in &#39;ActionDispatch::Routing::RouteSet::Dispatcher#dispatch&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="36" href="#">
            actionpack (8.0.3) lib/action_dispatch/routing/route_set.rb:50:in &#39;ActionDispatch::Routing::RouteSet::Dispatcher#serve&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="37" href="#">
            actionpack (8.0.3) lib/action_dispatch/journey/router.rb:53:in &#39;block in ActionDispatch::Journey::Router#serve&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="38" href="#">
            actionpack (8.0.3) lib/action_dispatch/journey/router.rb:133:in &#39;block in ActionDispatch::Journey::Router#find_routes&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="39" href="#">
            actionpack (8.0.3) lib/action_dispatch/journey/router.rb:126:in &#39;Array#each&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="40" href="#">
            actionpack (8.0.3) lib/action_dispatch/journey/router.rb:126:in &#39;ActionDispatch::Journey::Router#find_routes&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="41" href="#">
            actionpack (8.0.3) lib/action_dispatch/journey/router.rb:34:in &#39;ActionDispatch::Journey::Router#serve&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="42" href="#">
            actionpack (8.0.3) lib/action_dispatch/routing/route_set.rb:908:in &#39;ActionDispatch::Routing::RouteSet#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="43" href="#">
            omniauth (2.1.3) lib/omniauth/strategy.rb:202:in &#39;OmniAuth::Strategy#call!&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="44" href="#">
            omniauth (2.1.3) lib/omniauth/strategy.rb:169:in &#39;OmniAuth::Strategy#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="45" href="#">
            omniauth (2.1.3) lib/omniauth/strategy.rb:202:in &#39;OmniAuth::Strategy#call!&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="46" href="#">
            omniauth (2.1.3) lib/omniauth/strategy.rb:169:in &#39;OmniAuth::Strategy#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="47" href="#">
            omniauth (2.1.3) lib/omniauth/strategy.rb:202:in &#39;OmniAuth::Strategy#call!&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="48" href="#">
            omniauth (2.1.3) lib/omniauth/strategy.rb:169:in &#39;OmniAuth::Strategy#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="49" href="#">
            omniauth (2.1.3) lib/omniauth/builder.rb:44:in &#39;OmniAuth::Builder#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="50" href="#">
            rack-attack (6.7.0) lib/rack/attack.rb:127:in &#39;Rack::Attack#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="51" href="#">
            jsonapi-rails (0.4.1) lib/jsonapi/rails/filter_media_type.rb:16:in &#39;JSONAPI::Rails::FilterMediaType#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="52" href="#">
            lib/clearance/impersonation.rb:11:in &#39;Clearance::Impersonation#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="53" href="#">
            lib/unique_session_cookie.rb:14:in &#39;UniqueSessionCookie#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="54" href="#">
            rack-utm (0.0.2) lib/rack-utm.rb:59:in &#39;Rack::Utm#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="55" href="#">
            rack (3.2.1) lib/rack/deflater.rb:47:in &#39;Rack::Deflater#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="56" href="#">
            rack-canonical-host (1.3.0) lib/rack/canonical_host.rb:24:in &#39;Rack::CanonicalHost#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="57" href="#">
            rack (3.2.1) lib/rack/static.rb:162:in &#39;Rack::Static#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="58" href="#">
            clearance (2.10.0) lib/clearance/rack_session.rb:23:in &#39;Clearance::RackSession#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="59" href="#">
            lib/cookie_consent.rb:13:in &#39;CookieConsent#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="60" href="#">
            rack (3.2.1) lib/rack/tempfile_reaper.rb:20:in &#39;Rack::TempfileReaper#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="61" href="#">
            rack (3.2.1) lib/rack/etag.rb:29:in &#39;Rack::ETag#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="62" href="#">
            rack (3.2.1) lib/rack/conditional_get.rb:31:in &#39;Rack::ConditionalGet#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="63" href="#">
            rack (3.2.1) lib/rack/head.rb:15:in &#39;Rack::Head#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="64" href="#">
            actionpack (8.0.3) lib/action_dispatch/http/permissions_policy.rb:38:in &#39;ActionDispatch::PermissionsPolicy::Middleware#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="65" href="#">
            actionpack (8.0.3) lib/action_dispatch/http/content_security_policy.rb:38:in &#39;ActionDispatch::ContentSecurityPolicy::Middleware#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="66" href="#">
            rack-session (2.1.1) lib/rack/session/abstract/id.rb:274:in &#39;Rack::Session::Abstract::Persisted#context&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="67" href="#">
            rack-session (2.1.1) lib/rack/session/abstract/id.rb:268:in &#39;Rack::Session::Abstract::Persisted#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="68" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/cookies.rb:706:in &#39;ActionDispatch::Cookies#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="69" href="#">
            rack-strip-cookies (2.0.0) lib/rack/strip-cookies.rb:61:in &#39;Rack::StripCookies#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="70" href="#">
            activerecord (8.0.3) lib/active_record/migration.rb:670:in &#39;ActiveRecord::Migration::CheckPending#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="71" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/callbacks.rb:31:in &#39;block in ActionDispatch::Callbacks#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="72" href="#">
            activesupport (8.0.3) lib/active_support/callbacks.rb:100:in &#39;ActiveSupport::Callbacks#run_callbacks&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="73" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/callbacks.rb:30:in &#39;ActionDispatch::Callbacks#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="74" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/executor.rb:16:in &#39;ActionDispatch::Executor#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="75" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/actionable_exceptions.rb:18:in &#39;ActionDispatch::ActionableExceptions#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="76" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/debug_exceptions.rb:31:in &#39;ActionDispatch::DebugExceptions#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="77" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/show_exceptions.rb:32:in &#39;ActionDispatch::ShowExceptions#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="78" href="#">
            railties (8.0.3) lib/rails/rack/logger.rb:41:in &#39;Rails::Rack::Logger#call_app&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="79" href="#">
            railties (8.0.3) lib/rails/rack/logger.rb:29:in &#39;Rails::Rack::Logger#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="80" href="#">
            ahoy (5d10839c3997) lib/ahoy/engine.rb:22:in &#39;Rails::Rack::Logger#call_with_quiet_ahoy&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="81" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/remote_ip.rb:96:in &#39;ActionDispatch::RemoteIp#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="82" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/request_id.rb:34:in &#39;ActionDispatch::RequestId#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="83" href="#">
            rack (3.2.1) lib/rack/method_override.rb:28:in &#39;Rack::MethodOverride#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="84" href="#">
            rack (3.2.1) lib/rack/runtime.rb:24:in &#39;Rack::Runtime#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="85" href="#">
            activesupport (8.0.3) lib/active_support/cache/strategy/local_cache_middleware.rb:29:in &#39;ActiveSupport::Cache::Strategy::LocalCache::Middleware#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="86" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/server_timing.rb:61:in &#39;block in ActionDispatch::ServerTiming#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="87" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/server_timing.rb:26:in &#39;ActionDispatch::ServerTiming::Subscriber#collect_events&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="88" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/server_timing.rb:60:in &#39;ActionDispatch::ServerTiming#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="89" href="#">
            hotwire-livereload (2.0.0) lib/hotwire/livereload/middleware.rb:10:in &#39;Hotwire::Livereload::Middleware#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="90" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/executor.rb:16:in &#39;ActionDispatch::Executor#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="91" href="#">
            propshaft (1.3.1) lib/propshaft/server.rb:37:in &#39;Propshaft::Server#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="92" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/static.rb:27:in &#39;ActionDispatch::Static#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="93" href="#">
            rack (3.2.1) lib/rack/sendfile.rb:114:in &#39;Rack::Sendfile#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="94" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/host_authorization.rb:143:in &#39;ActionDispatch::HostAuthorization#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="95" href="#">
            rack-reverse-proxy (06f21feb6afb) lib/rack_reverse_proxy/roundtrip.rb:19:in &#39;RackReverseProxy::RoundTrip#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="96" href="#">
            rack-reverse-proxy (06f21feb6afb) lib/rack_reverse_proxy/middleware.rb:27:in &#39;RackReverseProxy::Middleware#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="97" href="#">
            rack-cors (3.0.0) lib/rack/cors.rb:102:in &#39;Rack::Cors#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="98" href="#">
            railties (8.0.3) lib/rails/engine.rb:535:in &#39;Rails::Engine#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="99" href="#">
            puma (7.0.4) lib/puma/configuration.rb:299:in &#39;Puma::Configuration::ConfigMiddleware#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="100" href="#">
            puma (7.0.4) lib/puma/request.rb:101:in &#39;block in Puma::Request#handle_request&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="101" href="#">
            puma (7.0.4) lib/puma/thread_pool.rb:346:in &#39;Puma::ThreadPool#with_force_shutdown&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="102" href="#">
            puma (7.0.4) lib/puma/request.rb:100:in &#39;Puma::Request#handle_request&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="103" href="#">
            puma (7.0.4) lib/puma/server.rb:506:in &#39;Puma::Server#process_client&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="104" href="#">
            puma (7.0.4) lib/puma/server.rb:265:in &#39;block in Puma::Server#run&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-1" data-exception-object-id="253304" data-frame-id="105" href="#">
            puma (7.0.4) lib/puma/thread_pool.rb:173:in &#39;block in Puma::ThreadPool#spawn_thread&#39;
          </a>
          <br>
      </code>
    </div>

  <script>
    (function() {
      var traceFrames = document.getElementsByClassName('trace-frames-1');
      var selectedFrame, currentSource = document.getElementById('frame-source-1-0');

      // Add click listeners for all stack frames
      for (var i = 0; i < traceFrames.length; i++) {
        traceFrames[i].addEventListener('click', function(e) {
          e.preventDefault();
          var target = e.target;
          var frame_id = target.dataset.frameId;

          if (selectedFrame) {
            selectedFrame.className = selectedFrame.className.replace("selected", "");
          }

          target.className += " selected";
          selectedFrame = target;

          // Change the extracted source code
          changeSourceExtract(frame_id);
        });

        function changeSourceExtract(frame_id) {
          var el = document.getElementById('frame-source-1-' + frame_id);
          if (currentSource && el) {
            currentSource.className += " hidden";
            el.className = el.className.replace(" hidden", "");
            currentSource = el;
          }
        }
      }
    })();
  </script>
</div>

    </div>
    <div class="details">
      <a class="summary" href="#" onclick="return toggle(253320)">
        Dry::Types::ConstraintError: nil violates constraints (type?(Analytics::ShipperIntentInsight(id: integer, feed_id: integer, analytics_company_id: integer, status: string, queued_at: datetime, complete_at: datetime, created_at: datetime, updated_at: datetime, uuid: uuid), nil) failed)
      </a>
    </div>

    <div id="253320" class="hidden">
      
    <div class="source hidden" id="frame-source-2-0">
      <div class="info">
        Extracted source (around line <strong>#37</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>35</span>
<span>36</span>
<span>37</span>
<span>38</span>
<span>39</span>
<span>40</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">          type.call_unsafe(input)
</div><div class="line">        else
</div><div class="line active">          <span class="error_highlight">raise</span> ConstraintError.new(result, input)
</div><div class="line">        end
</div><div class="line">      end
</div><div class="line">
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-1">
      <div class="info">
        Extracted source (around line <strong>#44</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>42</span>
<span>43</span>
<span>44</span>
<span>45</span>
<span>46</span>
<span>47</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">
</div><div class="line">        # @api private
</div><div class="line active">        def call_unsafe(input) = type<span class="error_highlight">.call_unsafe</span>(input)
</div><div class="line">
</div><div class="line">        # @see Dry::Types::Nominal#try
</div><div class="line">        #
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-2">
      <div class="info">
        Extracted source (around line <strong>#328</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>326</span>
<span>327</span>
<span>328</span>
<span>329</span>
<span>330</span>
<span>331</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">          if type
</div><div class="line">            begin
</div><div class="line active">              result[k] = type<span class="error_highlight">.call_unsafe</span>(value)
</div><div class="line">            rescue ConstraintError =&gt; e
</div><div class="line">              raise SchemaError.new(type.name, value, e.result)
</div><div class="line">            rescue CoercionError =&gt; e
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-3">
      <div class="info">
        Extracted source (around line <strong>#322</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>320</span>
<span>321</span>
<span>322</span>
<span>323</span>
<span>324</span>
<span>325</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">        result = {}
</div><div class="line">
</div><div class="line active">        hash<span class="error_highlight">.each</span> do |key, value|
</div><div class="line">          k = @transform_key.(key)
</div><div class="line">          type = @name_key_map[k]
</div><div class="line">
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-4">
      <div class="info">
        Extracted source (around line <strong>#322</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>320</span>
<span>321</span>
<span>322</span>
<span>323</span>
<span>324</span>
<span>325</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">        result = {}
</div><div class="line">
</div><div class="line active">        hash<span class="error_highlight">.each</span> do |key, value|
</div><div class="line">          k = @transform_key.(key)
</div><div class="line">          type = @name_key_map[k]
</div><div class="line">
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-5">
      <div class="info">
        Extracted source (around line <strong>#60</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>58</span>
<span>59</span>
<span>60</span>
<span>61</span>
<span>62</span>
<span>63</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      # @api private
</div><div class="line">      def call_unsafe(hash, options = EMPTY_HASH)
</div><div class="line active">        <span class="error_highlight">resolve_unsafe</span>(coerce(hash), options)
</div><div class="line">      end
</div><div class="line">
</div><div class="line">      # @param [Hash] hash
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-6">
      <div class="info">
        Extracted source (around line <strong>#80</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>78</span>
<span>79</span>
<span>80</span>
<span>81</span>
<span>82</span>
<span>83</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      #
</div><div class="line">      # @api private
</div><div class="line active">      def call_unsafe(input) = type<span class="error_highlight">.call_unsafe</span>(fn.(input))
</div><div class="line">
</div><div class="line">      # @param [Object] input
</div><div class="line">      # @param [#call,nil] block
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-7">
      <div class="info">
        Extracted source (around line <strong>#254</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>252</span>
<span>253</span>
<span>254</span>
<span>255</span>
<span>256</span>
<span>257</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">          load(schema.call_safe(attributes) { |output = attributes| return yield output })
</div><div class="line">        else
</div><div class="line active">          load(schema<span class="error_highlight">.call_unsafe</span>(attributes))
</div><div class="line">        end
</div><div class="line">      rescue Types::CoercionError =&gt; e
</div><div class="line">        raise Error, &quot;[#{self}.new] #{e}&quot;, e.backtrace
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source " id="frame-source-2-8">
      <div class="info">
        Extracted source (around line <strong>#14</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>12</span>
<span>13</span>
<span>14</span>
<span>15</span>
<span>16</span>
<span>17</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">
</div><div class="line">      render json: Analytics::ShipperEvents::Blueprints::Shipper.render(
</div><div class="line active">        Analytics::ShipperEvents::Shipper<span class="error_highlight">.new</span>(intent:, insight:)
</div><div class="line">      )
</div><div class="line">    end
</div><div class="line">
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-9">
      <div class="info">
        Extracted source (around line <strong>#8</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>6</span>
<span>7</span>
<span>8</span>
<span>9</span>
<span>10</span>
<span>11</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">  module BasicImplicitRender # :nodoc:
</div><div class="line">    def send_action(method, *args)
</div><div class="line active">      ret = super
</div><div class="line">      default_render unless performed?
</div><div class="line">      ret
</div><div class="line">    end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-10">
      <div class="info">
        Extracted source (around line <strong>#215</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>213</span>
<span>214</span>
<span>215</span>
<span>216</span>
<span>217</span>
<span>218</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      # necessarily the same as the action name.
</div><div class="line">      def process_action(...)
</div><div class="line active">        <span class="error_highlight">send_action</span>(...)
</div><div class="line">      end
</div><div class="line">
</div><div class="line">      # Actually call the method associated with the action. Override this method if
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-11">
      <div class="info">
        Extracted source (around line <strong>#193</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>191</span>
<span>192</span>
<span>193</span>
<span>194</span>
<span>195</span>
<span>196</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      def process_action(*) # :nodoc:
</div><div class="line">        self.formats = request.formats.filter_map(&amp;:ref)
</div><div class="line active">        super
</div><div class="line">      end
</div><div class="line">
</div><div class="line">      def _process_variant(options)
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-12">
      <div class="info">
        Extracted source (around line <strong>#261</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>259</span>
<span>260</span>
<span>261</span>
<span>262</span>
<span>263</span>
<span>264</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      def process_action(...)
</div><div class="line">        run_callbacks(:process_action) do
</div><div class="line active">          super
</div><div class="line">        end
</div><div class="line">      end
</div><div class="line">  end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-13">
      <div class="info">
        Extracted source (around line <strong>#120</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>118</span>
<span>119</span>
<span>120</span>
<span>121</span>
<span>122</span>
<span>123</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">              current.invoke_before(env)
</div><div class="line">              if current.final?
</div><div class="line active">                env.value = !env.halted &amp;&amp; (!block_given? || yield)
</div><div class="line">              elsif current.skip?(env)
</div><div class="line">                (skipped ||= []) &lt;&lt; current
</div><div class="line">                next_sequence = next_sequence.nested
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-14">
      <div class="info">
        Extracted source (around line <strong>#16</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>14</span>
<span>15</span>
<span>16</span>
<span>17</span>
<span>18</span>
<span>19</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      self.controller = controller
</div><div class="line">      STORED_DATA.each { |k, m| store[k] = send(m) }
</div><div class="line active">      yield
</div><div class="line">    ensure
</div><div class="line">      self.controller = nil
</div><div class="line">      STORED_DATA.keys.each { |k| store.delete(k) }
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-15">
      <div class="info">
        Extracted source (around line <strong>#129</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>127</span>
<span>128</span>
<span>129</span>
<span>130</span>
<span>131</span>
<span>132</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">                begin
</div><div class="line">                  target, block, method, *arguments = current.expand_call_template(env, invoke_sequence)
</div><div class="line active">                  target<span class="error_highlight">.send</span>(method, *arguments, &amp;block)
</div><div class="line">                ensure
</div><div class="line">                  next_sequence = current
</div><div class="line">                end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-16">
      <div class="info">
        Extracted source (around line <strong>#24</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>22</span>
<span>23</span>
<span>24</span>
<span>25</span>
<span>26</span>
<span>27</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">    def with_request_id(request_id)
</div><div class="line">      old_request_id, self.current_request_id = self.current_request_id, request_id
</div><div class="line active">      yield
</div><div class="line">    ensure
</div><div class="line">      self.current_request_id = old_request_id
</div><div class="line">    end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-17">
      <div class="info">
        Extracted source (around line <strong>#10</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>8</span>
<span>9</span>
<span>10</span>
<span>11</span>
<span>12</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">  private
</div><div class="line">    def turbo_tracking_request_id(&amp;block)
</div><div class="line active">      Turbo<span class="error_highlight">.with_request_id</span>(request.headers[&quot;X-Turbo-Request-Id&quot;], &amp;block)
</div><div class="line">    end
</div><div class="line">end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-18">
      <div class="info">
        Extracted source (around line <strong>#129</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>127</span>
<span>128</span>
<span>129</span>
<span>130</span>
<span>131</span>
<span>132</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">                begin
</div><div class="line">                  target, block, method, *arguments = current.expand_call_template(env, invoke_sequence)
</div><div class="line active">                  target<span class="error_highlight">.send</span>(method, *arguments, &amp;block)
</div><div class="line">                ensure
</div><div class="line">                  next_sequence = current
</div><div class="line">                end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-19">
      <div class="info">
        Extracted source (around line <strong>#16</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>14</span>
<span>15</span>
<span>16</span>
<span>17</span>
<span>18</span>
<span>19</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      self.controller = controller
</div><div class="line">      STORED_DATA.each { |k, m| store[k] = send(m) }
</div><div class="line active">      yield
</div><div class="line">    ensure
</div><div class="line">      self.controller = nil
</div><div class="line">      STORED_DATA.keys.each { |k| store.delete(k) }
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-20">
      <div class="info">
        Extracted source (around line <strong>#129</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>127</span>
<span>128</span>
<span>129</span>
<span>130</span>
<span>131</span>
<span>132</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">                begin
</div><div class="line">                  target, block, method, *arguments = current.expand_call_template(env, invoke_sequence)
</div><div class="line active">                  target<span class="error_highlight">.send</span>(method, *arguments, &amp;block)
</div><div class="line">                ensure
</div><div class="line">                  next_sequence = current
</div><div class="line">                end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-21">
      <div class="info">
        Extracted source (around line <strong>#50</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>48</span>
<span>49</span>
<span>50</span>
<span>51</span>
<span>52</span>
<span>53</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      begin
</div><div class="line">        Ahoy.instance = ahoy
</div><div class="line active">        yield
</div><div class="line">      ensure
</div><div class="line">        Ahoy.instance = previous_value
</div><div class="line">      end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-22">
      <div class="info">
        Extracted source (around line <strong>#129</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>127</span>
<span>128</span>
<span>129</span>
<span>130</span>
<span>131</span>
<span>132</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">                begin
</div><div class="line">                  target, block, method, *arguments = current.expand_call_template(env, invoke_sequence)
</div><div class="line active">                  target<span class="error_highlight">.send</span>(method, *arguments, &amp;block)
</div><div class="line">                ensure
</div><div class="line">                  next_sequence = current
</div><div class="line">                end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-23">
      <div class="info">
        Extracted source (around line <strong>#140</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>138</span>
<span>139</span>
<span>140</span>
<span>141</span>
<span>142</span>
<span>143</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">          end
</div><div class="line">
</div><div class="line active">          invoke_sequence<span class="error_highlight">.call</span>
</div><div class="line">        end
</div><div class="line">      end
</div><div class="line">    end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-24">
      <div class="info">
        Extracted source (around line <strong>#260</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>258</span>
<span>259</span>
<span>260</span>
<span>261</span>
<span>262</span>
<span>263</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      # callbacks around the normal behavior.
</div><div class="line">      def process_action(...)
</div><div class="line active">        <span class="error_highlight">run_callbacks</span>(:process_action) do
</div><div class="line">          super
</div><div class="line">        end
</div><div class="line">      end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-25">
      <div class="info">
        Extracted source (around line <strong>#27</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>25</span>
<span>26</span>
<span>27</span>
<span>28</span>
<span>29</span>
<span>30</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">    private
</div><div class="line">      def process_action(*)
</div><div class="line active">        super
</div><div class="line">      rescue Exception =&gt; exception
</div><div class="line">        request.env[&quot;action_dispatch.show_detailed_exceptions&quot;] ||= show_detailed_exceptions?
</div><div class="line">        rescue_with_handler(exception) || raise
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-26">
      <div class="info">
        Extracted source (around line <strong>#76</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>74</span>
<span>75</span>
<span>76</span>
<span>77</span>
<span>78</span>
<span>79</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">
</div><div class="line">        ActiveSupport::Notifications.instrument(&quot;process_action.action_controller&quot;, raw_payload) do |payload|
</div><div class="line active">          result = super
</div><div class="line">          payload[:response] = response
</div><div class="line">          payload[:status]   = response.status
</div><div class="line">          result
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-27">
      <div class="info">
        Extracted source (around line <strong>#210</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>208</span>
<span>209</span>
<span>210</span>
<span>211</span>
<span>212</span>
<span>213</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      def instrument(name, payload = {})
</div><div class="line">        if notifier.listening?(name)
</div><div class="line active">          instrumenter.instrument(name, payload) { yield payload if block_given? }
</div><div class="line">        else
</div><div class="line">          yield payload if block_given?
</div><div class="line">        end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-28">
      <div class="info">
        Extracted source (around line <strong>#58</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>56</span>
<span>57</span>
<span>58</span>
<span>59</span>
<span>60</span>
<span>61</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">        handle.start
</div><div class="line">        begin
</div><div class="line active">          yield payload if block_given?
</div><div class="line">        rescue Exception =&gt; e
</div><div class="line">          payload[:exception] = [e.class.name, e.message]
</div><div class="line">          payload[:exception_object] = e
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-29">
      <div class="info">
        Extracted source (around line <strong>#210</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>208</span>
<span>209</span>
<span>210</span>
<span>211</span>
<span>212</span>
<span>213</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      def instrument(name, payload = {})
</div><div class="line">        if notifier.listening?(name)
</div><div class="line active">          instrumenter<span class="error_highlight">.instrument</span>(name, payload) { yield payload if block_given? }
</div><div class="line">        else
</div><div class="line">          yield payload if block_given?
</div><div class="line">        end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-30">
      <div class="info">
        Extracted source (around line <strong>#75</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>73</span>
<span>74</span>
<span>75</span>
<span>76</span>
<span>77</span>
<span>78</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">        ActiveSupport::Notifications.instrument(&quot;start_processing.action_controller&quot;, raw_payload)
</div><div class="line">
</div><div class="line active">        ActiveSupport::Notifications<span class="error_highlight">.instrument</span>(&quot;process_action.action_controller&quot;, raw_payload) do |payload|
</div><div class="line">          result = super
</div><div class="line">          payload[:response] = response
</div><div class="line">          payload[:status]   = response.status
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-31">
      <div class="info">
        Extracted source (around line <strong>#259</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>257</span>
<span>258</span>
<span>259</span>
<span>260</span>
<span>261</span>
<span>262</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      def process_action(*)
</div><div class="line">        _perform_parameter_wrapping if _wrapper_enabled?
</div><div class="line active">        super
</div><div class="line">      end
</div><div class="line">
</div><div class="line">      # Returns the wrapper key which will be used to store wrapped parameters.
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-32">
      <div class="info">
        Extracted source (around line <strong>#39</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>37</span>
<span>38</span>
<span>39</span>
<span>40</span>
<span>41</span>
<span>42</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">          # and it won&#39;t be cleaned up by the method below.
</div><div class="line">          ActiveRecord::RuntimeRegistry.reset
</div><div class="line active">          super
</div><div class="line">        end
</div><div class="line">
</div><div class="line">        def cleanup_view_runtime
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-33">
      <div class="info">
        Extracted source (around line <strong>#152</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>150</span>
<span>151</span>
<span>152</span>
<span>153</span>
<span>154</span>
<span>155</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      @_response_body = nil
</div><div class="line">
</div><div class="line active">      <span class="error_highlight">process_action</span>(action_name, ...)
</div><div class="line">    end
</div><div class="line">
</div><div class="line">    # Delegates to the class&#39;s ::controller_path.
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-34">
      <div class="info">
        Extracted source (around line <strong>#40</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>38</span>
<span>39</span>
<span>40</span>
<span>41</span>
<span>42</span>
<span>43</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">    def process(...) # :nodoc:
</div><div class="line">      old_config, I18n.config = I18n.config, I18nProxy.new(I18n.config, lookup_context)
</div><div class="line active">      super
</div><div class="line">    ensure
</div><div class="line">      I18n.config = old_config
</div><div class="line">    end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-35">
      <div class="info">
        Extracted source (around line <strong>#252</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>250</span>
<span>251</span>
<span>252</span>
<span>253</span>
<span>254</span>
<span>255</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      set_request!(request)
</div><div class="line">      set_response!(response)
</div><div class="line active">      <span class="error_highlight">process</span>(name)
</div><div class="line">      request.commit_flash
</div><div class="line">      to_a
</div><div class="line">    end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-36">
      <div class="info">
        Extracted source (around line <strong>#335</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>333</span>
<span>334</span>
<span>335</span>
<span>336</span>
<span>337</span>
<span>338</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">        middleware_stack.build(name) { |env| new.dispatch(name, req, res) }.call req.env
</div><div class="line">      else
</div><div class="line active">        new<span class="error_highlight">.dispatch</span>(name, req, res)
</div><div class="line">      end
</div><div class="line">    end
</div><div class="line">  end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-37">
      <div class="info">
        Extracted source (around line <strong>#67</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>65</span>
<span>66</span>
<span>67</span>
<span>68</span>
<span>69</span>
<span>70</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">
</div><div class="line">          def dispatch(controller, action, req, res)
</div><div class="line active">            controller<span class="error_highlight">.dispatch</span>(action, req, res)
</div><div class="line">          end
</div><div class="line">      end
</div><div class="line">
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-38">
      <div class="info">
        Extracted source (around line <strong>#50</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>48</span>
<span>49</span>
<span>50</span>
<span>51</span>
<span>52</span>
<span>53</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">          controller = controller req
</div><div class="line">          res        = controller.make_response! req
</div><div class="line active">          <span class="error_highlight">dispatch</span>(controller, params[:action], req, res)
</div><div class="line">        rescue ActionController::RoutingError
</div><div class="line">          if @raise_on_name_error
</div><div class="line">            raise
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-39">
      <div class="info">
        Extracted source (around line <strong>#53</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>51</span>
<span>52</span>
<span>53</span>
<span>54</span>
<span>55</span>
<span>56</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">          req.route_uri_pattern = route.path.spec.to_s
</div><div class="line">
</div><div class="line active">          _, headers, _ = response = route.app<span class="error_highlight">.serve</span>(req)
</div><div class="line">
</div><div class="line">          if &quot;pass&quot; == headers[Constants::X_CASCADE]
</div><div class="line">            req.script_name     = script_name
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-40">
      <div class="info">
        Extracted source (around line <strong>#133</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>131</span>
<span>132</span>
<span>133</span>
<span>134</span>
<span>135</span>
<span>136</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">              path_parameters[name.to_sym] = Utils.unescape_uri(val) if val
</div><div class="line">            }
</div><div class="line active">            yield [match_data, path_parameters, r]
</div><div class="line">          }
</div><div class="line">        end
</div><div class="line">
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-41">
      <div class="info">
        Extracted source (around line <strong>#126</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>124</span>
<span>125</span>
<span>126</span>
<span>127</span>
<span>128</span>
<span>129</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">          routes.sort_by!(&amp;:precedence)
</div><div class="line">
</div><div class="line active">          routes<span class="error_highlight">.each</span> { |r|
</div><div class="line">            match_data = r.path.match(path_info)
</div><div class="line">            path_parameters = {}
</div><div class="line">            match_data.names.each_with_index { |name, i|
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-42">
      <div class="info">
        Extracted source (around line <strong>#126</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>124</span>
<span>125</span>
<span>126</span>
<span>127</span>
<span>128</span>
<span>129</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">          routes.sort_by!(&amp;:precedence)
</div><div class="line">
</div><div class="line active">          routes<span class="error_highlight">.each</span> { |r|
</div><div class="line">            match_data = r.path.match(path_info)
</div><div class="line">            path_parameters = {}
</div><div class="line">            match_data.names.each_with_index { |name, i|
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-43">
      <div class="info">
        Extracted source (around line <strong>#34</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>32</span>
<span>33</span>
<span>34</span>
<span>35</span>
<span>36</span>
<span>37</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">
</div><div class="line">      def serve(req)
</div><div class="line active">        <span class="error_highlight">find_routes</span>(req) do |match, parameters, route|
</div><div class="line">          set_params  = req.path_parameters
</div><div class="line">          path_info   = req.path_info
</div><div class="line">          script_name = req.script_name
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-44">
      <div class="info">
        Extracted source (around line <strong>#908</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>906</span>
<span>907</span>
<span>908</span>
<span>909</span>
<span>910</span>
<span>911</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">        req = make_request(env)
</div><div class="line">        req.path_info = Journey::Router::Utils.normalize_path(req.path_info)
</div><div class="line active">        @router<span class="error_highlight">.serve</span>(req)
</div><div class="line">      end
</div><div class="line">
</div><div class="line">      def recognize_path(path, environment = {})
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-45">
      <div class="info">
        Extracted source (around line <strong>#202</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>200</span>
<span>201</span>
<span>202</span>
<span>203</span>
<span>204</span>
<span>205</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      end
</div><div class="line">
</div><div class="line active">      @app<span class="error_highlight">.call</span>(env)
</div><div class="line">    end
</div><div class="line">
</div><div class="line">    def warn_if_using_get_on_request_path
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-46">
      <div class="info">
        Extracted source (around line <strong>#169</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>167</span>
<span>168</span>
<span>169</span>
<span>170</span>
<span>171</span>
<span>172</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">    # @param [Hash] The Rack environment.
</div><div class="line">    def call(env)
</div><div class="line active">      dup<span class="error_highlight">.call!</span>(env)
</div><div class="line">    end
</div><div class="line">
</div><div class="line">    # The logic for dispatching any additional actions that need
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-47">
      <div class="info">
        Extracted source (around line <strong>#202</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>200</span>
<span>201</span>
<span>202</span>
<span>203</span>
<span>204</span>
<span>205</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      end
</div><div class="line">
</div><div class="line active">      @app<span class="error_highlight">.call</span>(env)
</div><div class="line">    end
</div><div class="line">
</div><div class="line">    def warn_if_using_get_on_request_path
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-48">
      <div class="info">
        Extracted source (around line <strong>#169</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>167</span>
<span>168</span>
<span>169</span>
<span>170</span>
<span>171</span>
<span>172</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">    # @param [Hash] The Rack environment.
</div><div class="line">    def call(env)
</div><div class="line active">      dup<span class="error_highlight">.call!</span>(env)
</div><div class="line">    end
</div><div class="line">
</div><div class="line">    # The logic for dispatching any additional actions that need
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-49">
      <div class="info">
        Extracted source (around line <strong>#202</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>200</span>
<span>201</span>
<span>202</span>
<span>203</span>
<span>204</span>
<span>205</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      end
</div><div class="line">
</div><div class="line active">      @app<span class="error_highlight">.call</span>(env)
</div><div class="line">    end
</div><div class="line">
</div><div class="line">    def warn_if_using_get_on_request_path
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-50">
      <div class="info">
        Extracted source (around line <strong>#169</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>167</span>
<span>168</span>
<span>169</span>
<span>170</span>
<span>171</span>
<span>172</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">    # @param [Hash] The Rack environment.
</div><div class="line">    def call(env)
</div><div class="line active">      dup<span class="error_highlight">.call!</span>(env)
</div><div class="line">    end
</div><div class="line">
</div><div class="line">    # The logic for dispatching any additional actions that need
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-51">
      <div class="info">
        Extracted source (around line <strong>#44</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>42</span>
<span>43</span>
<span>44</span>
<span>45</span>
<span>46</span>
<span>47</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">
</div><div class="line">    def call(env)
</div><div class="line active">      to_app<span class="error_highlight">.call</span>(env)
</div><div class="line">    end
</div><div class="line">  end
</div><div class="line">end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-52">
      <div class="info">
        Extracted source (around line <strong>#127</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>125</span>
<span>126</span>
<span>127</span>
<span>128</span>
<span>129</span>
<span>130</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      else
</div><div class="line">        configuration.tracked?(request)
</div><div class="line active">        @app<span class="error_highlight">.call</span>(env)
</div><div class="line">      end
</div><div class="line">    end
</div><div class="line">  end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-53">
      <div class="info">
        Extracted source (around line <strong>#16</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>14</span>
<span>15</span>
<span>16</span>
<span>17</span>
<span>18</span>
<span>19</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">        return [406, {}, []] unless valid_accept?(env[&#39;HTTP_ACCEPT&#39;])
</div><div class="line">
</div><div class="line active">        @app<span class="error_highlight">.call</span>(env)
</div><div class="line">      end
</div><div class="line">
</div><div class="line">      private
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-54">
      <div class="info">
        Extracted source (around line <strong>#11</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>9</span>
<span>10</span>
<span>11</span>
<span>12</span>
<span>13</span>
<span>14</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">    def call(env)
</div><div class="line">      sign_in_as_user(env)
</div><div class="line active">      app<span class="error_highlight">.call</span>(env)
</div><div class="line">    end
</div><div class="line">
</div><div class="line">    private
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-55">
      <div class="info">
        Extracted source (around line <strong>#14</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>12</span>
<span>13</span>
<span>14</span>
<span>15</span>
<span>16</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">    cookies[NAME] = { expires: 1.year.from_now, httponly: true, value: SecureRandom.uuid } unless cookies[NAME]
</div><div class="line">    env[NAME] = cookies[NAME]
</div><div class="line active">    app<span class="error_highlight">.call</span>(env)
</div><div class="line">  end
</div><div class="line">end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-56">
      <div class="info">
        Extracted source (around line <strong>#59</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>57</span>
<span>58</span>
<span>59</span>
<span>60</span>
<span>61</span>
<span>62</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      end
</div><div class="line">
</div><div class="line active">      status, headers, body = @app<span class="error_highlight">.call</span>(env)
</div><div class="line">
</div><div class="line">      if source != cookie_tag
</div><div class="line">        bake_cookies(headers, source, medium, term, content, campaign, from, time, lp)
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-57">
      <div class="info">
        Extracted source (around line <strong>#47</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>45</span>
<span>46</span>
<span>47</span>
<span>48</span>
<span>49</span>
<span>50</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">
</div><div class="line">    def call(env)
</div><div class="line active">      status, headers, body = response = @app<span class="error_highlight">.call</span>(env)
</div><div class="line">
</div><div class="line">      unless should_deflate?(env, status, headers, body)
</div><div class="line">        return response
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-58">
      <div class="info">
        Extracted source (around line <strong>#24</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>22</span>
<span>23</span>
<span>24</span>
<span>25</span>
<span>26</span>
<span>27</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">
</div><div class="line">      if redirect.canonical?
</div><div class="line active">        app<span class="error_highlight">.call</span>(env)
</div><div class="line">      else
</div><div class="line">        redirect.response
</div><div class="line">      end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-59">
      <div class="info">
        Extracted source (around line <strong>#162</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>160</span>
<span>161</span>
<span>162</span>
<span>163</span>
<span>164</span>
<span>165</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">        response
</div><div class="line">      else
</div><div class="line active">        @app<span class="error_highlight">.call</span>(env)
</div><div class="line">      end
</div><div class="line">    end
</div><div class="line">
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-60">
      <div class="info">
        Extracted source (around line <strong>#23</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>21</span>
<span>22</span>
<span>23</span>
<span>24</span>
<span>25</span>
<span>26</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      session = Clearance::Session.new(env)
</div><div class="line">      env[:clearance] = session
</div><div class="line active">      response = @app<span class="error_highlight">.call</span>(env)
</div><div class="line">
</div><div class="line">      if session.authentication_successful?
</div><div class="line">        session.add_cookie_to_headers
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-61">
      <div class="info">
        Extracted source (around line <strong>#13</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>11</span>
<span>12</span>
<span>13</span>
<span>14</span>
<span>15</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">    cookies = ::ActionDispatch::Request.new(env).cookie_jar
</div><div class="line">    cookies[NAME] = { expires: 1.year.from_now, httponly: true, value: &#39;true&#39; } unless cookies[NAME]
</div><div class="line active">    app<span class="error_highlight">.call</span>(env)
</div><div class="line">  end
</div><div class="line">end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-62">
      <div class="info">
        Extracted source (around line <strong>#20</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>18</span>
<span>19</span>
<span>20</span>
<span>21</span>
<span>22</span>
<span>23</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">
</div><div class="line">      begin
</div><div class="line active">        _, _, body = response = @app<span class="error_highlight">.call</span>(env)
</div><div class="line">      rescue Exception
</div><div class="line">        env[RACK_TEMPFILES]&amp;.each(&amp;:close!)
</div><div class="line">        raise
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-63">
      <div class="info">
        Extracted source (around line <strong>#29</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>27</span>
<span>28</span>
<span>29</span>
<span>30</span>
<span>31</span>
<span>32</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">
</div><div class="line">    def call(env)
</div><div class="line active">      status, headers, body = response = @app<span class="error_highlight">.call</span>(env)
</div><div class="line">
</div><div class="line">      if etag_status?(status) &amp;&amp; body.respond_to?(:to_ary) &amp;&amp; !skip_caching?(headers)
</div><div class="line">        body = body.to_ary
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-64">
      <div class="info">
        Extracted source (around line <strong>#31</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>29</span>
<span>30</span>
<span>31</span>
<span>32</span>
<span>33</span>
<span>34</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      case env[REQUEST_METHOD]
</div><div class="line">      when &quot;GET&quot;, &quot;HEAD&quot;
</div><div class="line active">        status, headers, body = response = @app<span class="error_highlight">.call</span>(env)
</div><div class="line">
</div><div class="line">        if status == 200 &amp;&amp; fresh?(env, headers)
</div><div class="line">          response[0] = 304
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-65">
      <div class="info">
        Extracted source (around line <strong>#15</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>13</span>
<span>14</span>
<span>15</span>
<span>16</span>
<span>17</span>
<span>18</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">
</div><div class="line">    def call(env)
</div><div class="line active">      _, _, body = response = @app<span class="error_highlight">.call</span>(env)
</div><div class="line">
</div><div class="line">      if env[REQUEST_METHOD] == HEAD
</div><div class="line">        body.close if body.respond_to?(:close)
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-66">
      <div class="info">
        Extracted source (around line <strong>#38</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>36</span>
<span>37</span>
<span>38</span>
<span>39</span>
<span>40</span>
<span>41</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">
</div><div class="line">      def call(env)
</div><div class="line active">        _, headers, _ = response = @app<span class="error_highlight">.call</span>(env)
</div><div class="line">
</div><div class="line">        return response if policy_present?(headers)
</div><div class="line">
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-67">
      <div class="info">
        Extracted source (around line <strong>#38</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>36</span>
<span>37</span>
<span>38</span>
<span>39</span>
<span>40</span>
<span>41</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">
</div><div class="line">      def call(env)
</div><div class="line active">        status, headers, _ = response = @app<span class="error_highlight">.call</span>(env)
</div><div class="line">
</div><div class="line">        # Returning CSP headers with a 304 Not Modified is harmful, since nonces in the
</div><div class="line">        # new CSP headers might not match nonces in the cached HTML.
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-68">
      <div class="info">
        Extracted source (around line <strong>#274</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>272</span>
<span>273</span>
<span>274</span>
<span>275</span>
<span>276</span>
<span>277</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">          req = make_request env
</div><div class="line">          prepare_session(req)
</div><div class="line active">          status, headers, body = app<span class="error_highlight">.call</span>(req.env)
</div><div class="line">          res = Rack::Response::Raw.new status, headers
</div><div class="line">          commit_session(req, res)
</div><div class="line">          [status, headers, body]
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-69">
      <div class="info">
        Extracted source (around line <strong>#268</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>266</span>
<span>267</span>
<span>268</span>
<span>269</span>
<span>270</span>
<span>271</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">
</div><div class="line">        def call(env)
</div><div class="line active">          <span class="error_highlight">context</span>(env)
</div><div class="line">        end
</div><div class="line">
</div><div class="line">        def context(env, app = @app)
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-70">
      <div class="info">
        Extracted source (around line <strong>#706</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>704</span>
<span>705</span>
<span>706</span>
<span>707</span>
<span>708</span>
<span>709</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">    def call(env)
</div><div class="line">      request = ActionDispatch::Request.new(env)
</div><div class="line active">      response = @app<span class="error_highlight">.call</span>(env)
</div><div class="line">
</div><div class="line">      if request.have_cookie_jar?
</div><div class="line">        cookie_jar = request.cookie_jar
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-71">
      <div class="info">
        Extracted source (around line <strong>#61</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>59</span>
<span>60</span>
<span>61</span>
<span>62</span>
<span>63</span>
<span>64</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">        # If cookies are not to be stripped, simply call the next middleware or application.
</div><div class="line">        # The original request and response headers remain untouched.
</div><div class="line active">        status, headers, body = @app<span class="error_highlight">.call</span>(env)
</div><div class="line">      end
</div><div class="line">
</div><div class="line">      # Return the final response to the client.
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-72">
      <div class="info">
        Extracted source (around line <strong>#670</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>668</span>
<span>669</span>
<span>670</span>
<span>671</span>
<span>672</span>
<span>673</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">        end
</div><div class="line">
</div><div class="line active">        @app<span class="error_highlight">.call</span>(env)
</div><div class="line">      end
</div><div class="line">
</div><div class="line">      private
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-73">
      <div class="info">
        Extracted source (around line <strong>#31</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>29</span>
<span>30</span>
<span>31</span>
<span>32</span>
<span>33</span>
<span>34</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      error = nil
</div><div class="line">      result = run_callbacks :call do
</div><div class="line active">        @app<span class="error_highlight">.call</span>(env)
</div><div class="line">      rescue =&gt; error
</div><div class="line">      end
</div><div class="line">      raise error if error
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-74">
      <div class="info">
        Extracted source (around line <strong>#100</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>98</span>
<span>99</span>
<span>100</span>
<span>101</span>
<span>102</span>
<span>103</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">
</div><div class="line">      if callbacks.empty?
</div><div class="line active">        yield if block_given?
</div><div class="line">      else
</div><div class="line">        env = Filters::Environment.new(self, false, nil)
</div><div class="line">
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-75">
      <div class="info">
        Extracted source (around line <strong>#30</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>28</span>
<span>29</span>
<span>30</span>
<span>31</span>
<span>32</span>
<span>33</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">    def call(env)
</div><div class="line">      error = nil
</div><div class="line active">      result = <span class="error_highlight">run_callbacks</span> :call do
</div><div class="line">        @app.call(env)
</div><div class="line">      rescue =&gt; error
</div><div class="line">      end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-76">
      <div class="info">
        Extracted source (around line <strong>#16</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>14</span>
<span>15</span>
<span>16</span>
<span>17</span>
<span>18</span>
<span>19</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      state = @executor.run!(reset: true)
</div><div class="line">      begin
</div><div class="line active">        response = @app<span class="error_highlight">.call</span>(env)
</div><div class="line">
</div><div class="line">        if env[&quot;action_dispatch.report_exception&quot;]
</div><div class="line">          error = env[&quot;action_dispatch.exception&quot;]
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-77">
      <div class="info">
        Extracted source (around line <strong>#18</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>16</span>
<span>17</span>
<span>18</span>
<span>19</span>
<span>20</span>
<span>21</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">    def call(env)
</div><div class="line">      request = ActionDispatch::Request.new(env)
</div><div class="line active">      return @app<span class="error_highlight">.call</span>(env) unless actionable_request?(request)
</div><div class="line">
</div><div class="line">      ActiveSupport::ActionableError.dispatch(request.params[:error].to_s.safe_constantize, request.params[:action])
</div><div class="line">
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-78">
      <div class="info">
        Extracted source (around line <strong>#31</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>29</span>
<span>30</span>
<span>31</span>
<span>32</span>
<span>33</span>
<span>34</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">
</div><div class="line">    def call(env)
</div><div class="line active">      _, headers, body = response = @app<span class="error_highlight">.call</span>(env)
</div><div class="line">
</div><div class="line">      if headers[Constants::X_CASCADE] == &quot;pass&quot;
</div><div class="line">        body.close if body.respond_to?(:close)
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-79">
      <div class="info">
        Extracted source (around line <strong>#32</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>30</span>
<span>31</span>
<span>32</span>
<span>33</span>
<span>34</span>
<span>35</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">
</div><div class="line">    def call(env)
</div><div class="line active">      @app<span class="error_highlight">.call</span>(env)
</div><div class="line">    rescue Exception =&gt; exception
</div><div class="line">      request = ActionDispatch::Request.new env
</div><div class="line">      backtrace_cleaner = request.get_header(&quot;action_dispatch.backtrace_cleaner&quot;)
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-80">
      <div class="info">
        Extracted source (around line <strong>#41</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>39</span>
<span>40</span>
<span>41</span>
<span>42</span>
<span>43</span>
<span>44</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">
</div><div class="line">          logger.info { started_request_message(request) }
</div><div class="line active">          status, headers, body = response = @app<span class="error_highlight">.call</span>(env)
</div><div class="line">          body = ::Rack::BodyProxy.new(body) { finish_request_instrumentation(handle, logger_tag_pop_count) }
</div><div class="line">
</div><div class="line">          if response.frozen?
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-81">
      <div class="info">
        Extracted source (around line <strong>#29</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>27</span>
<span>28</span>
<span>29</span>
<span>30</span>
<span>31</span>
<span>32</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">        end
</div><div class="line">
</div><div class="line active">        <span class="error_highlight">call_app</span>(request, env)
</div><div class="line">      end
</div><div class="line">
</div><div class="line">      private
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-82">
      <div class="info">
        Extracted source (around line <strong>#22</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>20</span>
<span>21</span>
<span>22</span>
<span>23</span>
<span>24</span>
<span>25</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">            end
</div><div class="line">          else
</div><div class="line active">            <span class="error_highlight">call_without_quiet_ahoy</span>(env)
</div><div class="line">          end
</div><div class="line">        end
</div><div class="line">        alias_method :call_without_quiet_ahoy, :call
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-83">
      <div class="info">
        Extracted source (around line <strong>#96</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>94</span>
<span>95</span>
<span>96</span>
<span>97</span>
<span>98</span>
<span>99</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      req = ActionDispatch::Request.new env
</div><div class="line">      req.remote_ip = GetIp.new(req, check_ip, proxies)
</div><div class="line active">      @app<span class="error_highlight">.call</span>(req.env)
</div><div class="line">    end
</div><div class="line">
</div><div class="line">    # The GetIp class exists as a way to defer processing of the request data into
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-84">
      <div class="info">
        Extracted source (around line <strong>#34</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>32</span>
<span>33</span>
<span>34</span>
<span>35</span>
<span>36</span>
<span>37</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      req = ActionDispatch::Request.new env
</div><div class="line">      req.request_id = make_request_id(req.get_header(@env_header))
</div><div class="line active">      @app<span class="error_highlight">.call</span>(env).tap { |_status, headers, _body| headers[@header] = req.request_id }
</div><div class="line">    end
</div><div class="line">
</div><div class="line">    private
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-85">
      <div class="info">
        Extracted source (around line <strong>#28</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>26</span>
<span>27</span>
<span>28</span>
<span>29</span>
<span>30</span>
<span>31</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      end
</div><div class="line">
</div><div class="line active">      @app<span class="error_highlight">.call</span>(env)
</div><div class="line">    end
</div><div class="line">
</div><div class="line">    def method_override(env)
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-86">
      <div class="info">
        Extracted source (around line <strong>#24</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>22</span>
<span>23</span>
<span>24</span>
<span>25</span>
<span>26</span>
<span>27</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">    def call(env)
</div><div class="line">      start_time = Utils.clock_time
</div><div class="line active">      _, headers, _ = response = @app<span class="error_highlight">.call</span>(env)
</div><div class="line">
</div><div class="line">      request_time = Utils.clock_time - start_time
</div><div class="line">
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-87">
      <div class="info">
        Extracted source (around line <strong>#29</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>27</span>
<span>28</span>
<span>29</span>
<span>30</span>
<span>31</span>
<span>32</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">          def call(env)
</div><div class="line">            LocalCacheRegistry.set_cache_for(local_cache_key, LocalStore.new)
</div><div class="line active">            response = @app<span class="error_highlight">.call</span>(env)
</div><div class="line">            response[2] = ::Rack::BodyProxy.new(response[2]) do
</div><div class="line">              LocalCacheRegistry.set_cache_for(local_cache_key, nil)
</div><div class="line">            end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-88">
      <div class="info">
        Extracted source (around line <strong>#61</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>59</span>
<span>60</span>
<span>61</span>
<span>62</span>
<span>63</span>
<span>64</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      response = nil
</div><div class="line">      events = @subscriber.collect_events do
</div><div class="line active">        response = @app<span class="error_highlight">.call</span>(env)
</div><div class="line">      end
</div><div class="line">
</div><div class="line">      headers = response[1]
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-89">
      <div class="info">
        Extracted source (around line <strong>#26</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>24</span>
<span>25</span>
<span>26</span>
<span>27</span>
<span>28</span>
<span>29</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">        events = []
</div><div class="line">        ActiveSupport::IsolatedExecutionState[KEY] = events
</div><div class="line active">        yield
</div><div class="line">        events
</div><div class="line">      ensure
</div><div class="line">        ActiveSupport::IsolatedExecutionState.delete(KEY)
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-90">
      <div class="info">
        Extracted source (around line <strong>#60</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>58</span>
<span>59</span>
<span>60</span>
<span>61</span>
<span>62</span>
<span>63</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">    def call(env)
</div><div class="line">      response = nil
</div><div class="line active">      events = @subscriber<span class="error_highlight">.collect_events</span> do
</div><div class="line">        response = @app.call(env)
</div><div class="line">      end
</div><div class="line">
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-91">
      <div class="info">
        Extracted source (around line <strong>#10</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>8</span>
<span>9</span>
<span>10</span>
<span>11</span>
<span>12</span>
<span>13</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      def call(env)
</div><div class="line">        request = ActionDispatch::Request.new(env)
</div><div class="line active">        status, headers, response = @app<span class="error_highlight">.call</span>(env)
</div><div class="line">
</div><div class="line">        if html_response?(headers)
</div><div class="line">          body = get_response_body(response)
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-92">
      <div class="info">
        Extracted source (around line <strong>#16</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>14</span>
<span>15</span>
<span>16</span>
<span>17</span>
<span>18</span>
<span>19</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      state = @executor.run!(reset: true)
</div><div class="line">      begin
</div><div class="line active">        response = @app<span class="error_highlight">.call</span>(env)
</div><div class="line">
</div><div class="line">        if env[&quot;action_dispatch.report_exception&quot;]
</div><div class="line">          error = env[&quot;action_dispatch.exception&quot;]
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-93">
      <div class="info">
        Extracted source (around line <strong>#37</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>35</span>
<span>36</span>
<span>37</span>
<span>38</span>
<span>39</span>
<span>40</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      end
</div><div class="line">    else
</div><div class="line active">      @app<span class="error_highlight">.call</span>(env)
</div><div class="line">    end
</div><div class="line">  end
</div><div class="line">
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-94">
      <div class="info">
        Extracted source (around line <strong>#27</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>25</span>
<span>26</span>
<span>27</span>
<span>28</span>
<span>29</span>
<span>30</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">
</div><div class="line">    def call(env)
</div><div class="line active">      @file_handler.attempt(env) || @app<span class="error_highlight">.call</span>(env)
</div><div class="line">    end
</div><div class="line">  end
</div><div class="line">
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-95">
      <div class="info">
        Extracted source (around line <strong>#114</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>112</span>
<span>113</span>
<span>114</span>
<span>115</span>
<span>116</span>
<span>117</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">
</div><div class="line">    def call(env)
</div><div class="line active">      _, headers, body = response = @app<span class="error_highlight">.call</span>(env)
</div><div class="line">
</div><div class="line">      if body.respond_to?(:to_path)
</div><div class="line">        case type = variation(env)
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-96">
      <div class="info">
        Extracted source (around line <strong>#143</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>141</span>
<span>142</span>
<span>143</span>
<span>144</span>
<span>145</span>
<span>146</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      if hosts.empty? || excluded?(request)
</div><div class="line">        mark_as_authorized(request)
</div><div class="line active">        @app<span class="error_highlight">.call</span>(env)
</div><div class="line">      else
</div><div class="line">        env[&quot;action_dispatch.blocked_hosts&quot;] = hosts
</div><div class="line">        @response_app.call(env)
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-97">
      <div class="info">
        Extracted source (around line <strong>#19</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>17</span>
<span>18</span>
<span>19</span>
<span>20</span>
<span>21</span>
<span>22</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">
</div><div class="line">    def call
</div><div class="line active">      return app<span class="error_highlight">.call</span>(env) if rule.nil?
</div><div class="line">      return proxy_with_newrelic if new_relic?
</div><div class="line">      proxy
</div><div class="line">    end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-98">
      <div class="info">
        Extracted source (around line <strong>#27</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>25</span>
<span>26</span>
<span>27</span>
<span>28</span>
<span>29</span>
<span>30</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">
</div><div class="line">    def call(env)
</div><div class="line active">      RoundTrip.new(@app, env, @global_options, @rules)<span class="error_highlight">.call</span>
</div><div class="line">    end
</div><div class="line">
</div><div class="line">    private
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-99">
      <div class="info">
        Extracted source (around line <strong>#102</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>100</span>
<span>101</span>
<span>102</span>
<span>103</span>
<span>104</span>
<span>105</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      vary_resource = resource_for_path(path)
</div><div class="line">
</div><div class="line active">      status, headers, body = @app<span class="error_highlight">.call</span> env
</div><div class="line">
</div><div class="line">      if add_headers
</div><div class="line">        headers = add_headers.merge(headers)
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-100">
      <div class="info">
        Extracted source (around line <strong>#535</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>533</span>
<span>534</span>
<span>535</span>
<span>536</span>
<span>537</span>
<span>538</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">    def call(env)
</div><div class="line">      req = build_request env
</div><div class="line active">      app<span class="error_highlight">.call</span> req.env
</div><div class="line">    end
</div><div class="line">
</div><div class="line">    # Defines additional Rack env configuration that is added on each call.
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-101">
      <div class="info">
        Extracted source (around line <strong>#299</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>297</span>
<span>298</span>
<span>299</span>
<span>300</span>
<span>301</span>
<span>302</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      def call(env)
</div><div class="line">        env[Const::PUMA_CONFIG] = @config
</div><div class="line active">        @app<span class="error_highlight">.call</span>(env)
</div><div class="line">      end
</div><div class="line">    end
</div><div class="line">
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-102">
      <div class="info">
        Extracted source (around line <strong>#101</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>99</span>
<span>100</span>
<span>101</span>
<span>102</span>
<span>103</span>
<span>104</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">        if @supported_http_methods == :any || @supported_http_methods.key?(env[REQUEST_METHOD])
</div><div class="line">          status, headers, app_body = @thread_pool.with_force_shutdown do
</div><div class="line active">            @app<span class="error_highlight">.call</span>(env)
</div><div class="line">          end
</div><div class="line">        else
</div><div class="line">          @log_writer.log &quot;Unsupported HTTP method used: #{env[REQUEST_METHOD]}&quot;
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-103">
      <div class="info">
        Extracted source (around line <strong>#346</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>344</span>
<span>345</span>
<span>346</span>
<span>347</span>
<span>348</span>
<span>349</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">        t[:with_force_shutdown] = true
</div><div class="line">      end
</div><div class="line active">      yield
</div><div class="line">    ensure
</div><div class="line">      t[:with_force_shutdown] = false
</div><div class="line">    end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-104">
      <div class="info">
        Extracted source (around line <strong>#100</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>98</span>
<span>99</span>
<span>100</span>
<span>101</span>
<span>102</span>
<span>103</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      begin
</div><div class="line">        if @supported_http_methods == :any || @supported_http_methods.key?(env[REQUEST_METHOD])
</div><div class="line active">          status, headers, app_body = @thread_pool<span class="error_highlight">.with_force_shutdown</span> do
</div><div class="line">            @app.call(env)
</div><div class="line">          end
</div><div class="line">        else
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-105">
      <div class="info">
        Extracted source (around line <strong>#506</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>504</span>
<span>505</span>
<span>506</span>
<span>507</span>
<span>508</span>
<span>509</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">
</div><div class="line">        @requests_count += 1
</div><div class="line active">        case <span class="error_highlight">handle_request</span>(client, requests + 1)
</div><div class="line">        when false
</div><div class="line">        when :async
</div><div class="line">          close_socket = false
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-106">
      <div class="info">
        Extracted source (around line <strong>#265</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>263</span>
<span>264</span>
<span>265</span>
<span>266</span>
<span>267</span>
<span>268</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">      @status = :run
</div><div class="line">
</div><div class="line active">      @thread_pool = ThreadPool.new(thread_name, options) { |client| <span class="error_highlight">process_client</span> client }
</div><div class="line">
</div><div class="line">      if @queue_requests
</div><div class="line">        @reactor = Reactor.new(@io_selector_backend) { |c|
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="source hidden" id="frame-source-2-107">
      <div class="info">
        Extracted source (around line <strong>#173</strong>):
      </div>
      <div class="data">
        <table cellpadding="0" cellspacing="0" class="lines">
          <tr>
            <td>
              <pre class="line_numbers">
<span>171</span>
<span>172</span>
<span>173</span>
<span>174</span>
<span>175</span>
<span>176</span>
              </pre>
            </td>
<td width="100%">
<pre>
<div class="line">
</div><div class="line">          begin
</div><div class="line active">            @out_of_band_pending = true if block<span class="error_highlight">.call</span>(work)
</div><div class="line">          rescue Exception =&gt; e
</div><div class="line">            STDERR.puts &quot;Error reached top of thread-pool: #{e.message} (#{e.class})&quot;
</div><div class="line">          end
</div>
</pre>
</td>
          </tr>
        </table>
      </div>
    </div>

      
<p><code>Rails.root: /Users/<USER>/github/carriersource/carrier_source</code></p>

<div id="traces-2">
    <a href="#" onclick="hide(&#39;Framework-Trace-2&#39;);hide(&#39;Full-Trace-2&#39;);show(&#39;Application-Trace-2&#39;);; return false;">Application Trace</a> |
    <a href="#" onclick="hide(&#39;Application-Trace-2&#39;);hide(&#39;Full-Trace-2&#39;);show(&#39;Framework-Trace-2&#39;);; return false;">Framework Trace</a> |
    <a href="#" onclick="hide(&#39;Application-Trace-2&#39;);hide(&#39;Framework-Trace-2&#39;);show(&#39;Full-Trace-2&#39;);; return false;">Full Trace</a> 

    <div id="Application-Trace-2" style="display: block;">
      <code class="traces">
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="8" href="#">
            app/controllers/analytics/hubspot_companies_controller.rb:14:in &#39;Analytics::HubspotCompaniesController#show&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="54" href="#">
            lib/clearance/impersonation.rb:11:in &#39;Clearance::Impersonation#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="55" href="#">
            lib/unique_session_cookie.rb:14:in &#39;UniqueSessionCookie#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="61" href="#">
            lib/cookie_consent.rb:13:in &#39;CookieConsent#call&#39;
          </a>
          <br>
      </code>
    </div>
    <div id="Framework-Trace-2" style="display: none;">
      <code class="traces">
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="0" href="#">
            dry-types (1.8.3) lib/dry/types/constrained.rb:37:in &#39;Dry::Types::Constrained#call_unsafe&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="1" href="#">
            dry-types (1.8.3) lib/dry/types/schema/key.rb:44:in &#39;Dry::Types::Schema::Key#call_unsafe&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="2" href="#">
            dry-types (1.8.3) lib/dry/types/schema.rb:328:in &#39;block in Dry::Types::Schema#resolve_unsafe&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="3" href="#">
            dry-types (1.8.3) lib/dry/types/schema.rb:322:in &#39;Hash#each&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="4" href="#">
            dry-types (1.8.3) lib/dry/types/schema.rb:322:in &#39;Dry::Types::Schema#resolve_unsafe&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="5" href="#">
            dry-types (1.8.3) lib/dry/types/schema.rb:60:in &#39;Dry::Types::Schema#call_unsafe&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="6" href="#">
            dry-types (1.8.3) lib/dry/types/constructor.rb:80:in &#39;Dry::Types::Constructor#call_unsafe&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="7" href="#">
            dry-struct (1.8.0) lib/dry/struct/class_interface.rb:254:in &#39;Dry::Struct::ClassInterface#new&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="9" href="#">
            actionpack (8.0.3) lib/action_controller/metal/basic_implicit_render.rb:8:in &#39;ActionController::BasicImplicitRender#send_action&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="10" href="#">
            actionpack (8.0.3) lib/abstract_controller/base.rb:215:in &#39;AbstractController::Base#process_action&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="11" href="#">
            actionpack (8.0.3) lib/action_controller/metal/rendering.rb:193:in &#39;ActionController::Rendering#process_action&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="12" href="#">
            actionpack (8.0.3) lib/abstract_controller/callbacks.rb:261:in &#39;block in AbstractController::Callbacks#process_action&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="13" href="#">
            activesupport (8.0.3) lib/active_support/callbacks.rb:120:in &#39;block in ActiveSupport::Callbacks#run_callbacks&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="14" href="#">
            audited (5.8.0) lib/audited/sweeper.rb:16:in &#39;Audited::Sweeper#around&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="15" href="#">
            activesupport (8.0.3) lib/active_support/callbacks.rb:129:in &#39;block in ActiveSupport::Callbacks#run_callbacks&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="16" href="#">
            turbo-rails (2.0.16) lib/turbo-rails.rb:24:in &#39;Turbo.with_request_id&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="17" href="#">
            turbo-rails (2.0.16) app/controllers/concerns/turbo/request_id_tracking.rb:10:in &#39;Turbo::RequestIdTracking#turbo_tracking_request_id&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="18" href="#">
            activesupport (8.0.3) lib/active_support/callbacks.rb:129:in &#39;block in ActiveSupport::Callbacks#run_callbacks&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="19" href="#">
            audited (5.8.0) lib/audited/sweeper.rb:16:in &#39;Audited::Sweeper#around&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="20" href="#">
            activesupport (8.0.3) lib/active_support/callbacks.rb:129:in &#39;block in ActiveSupport::Callbacks#run_callbacks&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="21" href="#">
            ahoy (5d10839c3997) lib/ahoy/controller.rb:50:in &#39;Ahoy::Controller#set_ahoy_request_store&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="22" href="#">
            activesupport (8.0.3) lib/active_support/callbacks.rb:129:in &#39;block in ActiveSupport::Callbacks#run_callbacks&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="23" href="#">
            activesupport (8.0.3) lib/active_support/callbacks.rb:140:in &#39;ActiveSupport::Callbacks#run_callbacks&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="24" href="#">
            actionpack (8.0.3) lib/abstract_controller/callbacks.rb:260:in &#39;AbstractController::Callbacks#process_action&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="25" href="#">
            actionpack (8.0.3) lib/action_controller/metal/rescue.rb:27:in &#39;ActionController::Rescue#process_action&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="26" href="#">
            actionpack (8.0.3) lib/action_controller/metal/instrumentation.rb:76:in &#39;block in ActionController::Instrumentation#process_action&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="27" href="#">
            activesupport (8.0.3) lib/active_support/notifications.rb:210:in &#39;block in ActiveSupport::Notifications.instrument&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="28" href="#">
            activesupport (8.0.3) lib/active_support/notifications/instrumenter.rb:58:in &#39;ActiveSupport::Notifications::Instrumenter#instrument&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="29" href="#">
            activesupport (8.0.3) lib/active_support/notifications.rb:210:in &#39;ActiveSupport::Notifications.instrument&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="30" href="#">
            actionpack (8.0.3) lib/action_controller/metal/instrumentation.rb:75:in &#39;ActionController::Instrumentation#process_action&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="31" href="#">
            actionpack (8.0.3) lib/action_controller/metal/params_wrapper.rb:259:in &#39;ActionController::ParamsWrapper#process_action&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="32" href="#">
            activerecord (8.0.3) lib/active_record/railties/controller_runtime.rb:39:in &#39;ActiveRecord::Railties::ControllerRuntime#process_action&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="33" href="#">
            actionpack (8.0.3) lib/abstract_controller/base.rb:152:in &#39;AbstractController::Base#process&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="34" href="#">
            actionview (8.0.3) lib/action_view/rendering.rb:40:in &#39;ActionView::Rendering#process&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="35" href="#">
            actionpack (8.0.3) lib/action_controller/metal.rb:252:in &#39;ActionController::Metal#dispatch&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="36" href="#">
            actionpack (8.0.3) lib/action_controller/metal.rb:335:in &#39;ActionController::Metal.dispatch&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="37" href="#">
            actionpack (8.0.3) lib/action_dispatch/routing/route_set.rb:67:in &#39;ActionDispatch::Routing::RouteSet::Dispatcher#dispatch&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="38" href="#">
            actionpack (8.0.3) lib/action_dispatch/routing/route_set.rb:50:in &#39;ActionDispatch::Routing::RouteSet::Dispatcher#serve&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="39" href="#">
            actionpack (8.0.3) lib/action_dispatch/journey/router.rb:53:in &#39;block in ActionDispatch::Journey::Router#serve&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="40" href="#">
            actionpack (8.0.3) lib/action_dispatch/journey/router.rb:133:in &#39;block in ActionDispatch::Journey::Router#find_routes&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="41" href="#">
            actionpack (8.0.3) lib/action_dispatch/journey/router.rb:126:in &#39;Array#each&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="42" href="#">
            actionpack (8.0.3) lib/action_dispatch/journey/router.rb:126:in &#39;ActionDispatch::Journey::Router#find_routes&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="43" href="#">
            actionpack (8.0.3) lib/action_dispatch/journey/router.rb:34:in &#39;ActionDispatch::Journey::Router#serve&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="44" href="#">
            actionpack (8.0.3) lib/action_dispatch/routing/route_set.rb:908:in &#39;ActionDispatch::Routing::RouteSet#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="45" href="#">
            omniauth (2.1.3) lib/omniauth/strategy.rb:202:in &#39;OmniAuth::Strategy#call!&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="46" href="#">
            omniauth (2.1.3) lib/omniauth/strategy.rb:169:in &#39;OmniAuth::Strategy#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="47" href="#">
            omniauth (2.1.3) lib/omniauth/strategy.rb:202:in &#39;OmniAuth::Strategy#call!&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="48" href="#">
            omniauth (2.1.3) lib/omniauth/strategy.rb:169:in &#39;OmniAuth::Strategy#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="49" href="#">
            omniauth (2.1.3) lib/omniauth/strategy.rb:202:in &#39;OmniAuth::Strategy#call!&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="50" href="#">
            omniauth (2.1.3) lib/omniauth/strategy.rb:169:in &#39;OmniAuth::Strategy#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="51" href="#">
            omniauth (2.1.3) lib/omniauth/builder.rb:44:in &#39;OmniAuth::Builder#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="52" href="#">
            rack-attack (6.7.0) lib/rack/attack.rb:127:in &#39;Rack::Attack#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="53" href="#">
            jsonapi-rails (0.4.1) lib/jsonapi/rails/filter_media_type.rb:16:in &#39;JSONAPI::Rails::FilterMediaType#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="56" href="#">
            rack-utm (0.0.2) lib/rack-utm.rb:59:in &#39;Rack::Utm#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="57" href="#">
            rack (3.2.1) lib/rack/deflater.rb:47:in &#39;Rack::Deflater#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="58" href="#">
            rack-canonical-host (1.3.0) lib/rack/canonical_host.rb:24:in &#39;Rack::CanonicalHost#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="59" href="#">
            rack (3.2.1) lib/rack/static.rb:162:in &#39;Rack::Static#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="60" href="#">
            clearance (2.10.0) lib/clearance/rack_session.rb:23:in &#39;Clearance::RackSession#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="62" href="#">
            rack (3.2.1) lib/rack/tempfile_reaper.rb:20:in &#39;Rack::TempfileReaper#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="63" href="#">
            rack (3.2.1) lib/rack/etag.rb:29:in &#39;Rack::ETag#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="64" href="#">
            rack (3.2.1) lib/rack/conditional_get.rb:31:in &#39;Rack::ConditionalGet#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="65" href="#">
            rack (3.2.1) lib/rack/head.rb:15:in &#39;Rack::Head#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="66" href="#">
            actionpack (8.0.3) lib/action_dispatch/http/permissions_policy.rb:38:in &#39;ActionDispatch::PermissionsPolicy::Middleware#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="67" href="#">
            actionpack (8.0.3) lib/action_dispatch/http/content_security_policy.rb:38:in &#39;ActionDispatch::ContentSecurityPolicy::Middleware#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="68" href="#">
            rack-session (2.1.1) lib/rack/session/abstract/id.rb:274:in &#39;Rack::Session::Abstract::Persisted#context&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="69" href="#">
            rack-session (2.1.1) lib/rack/session/abstract/id.rb:268:in &#39;Rack::Session::Abstract::Persisted#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="70" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/cookies.rb:706:in &#39;ActionDispatch::Cookies#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="71" href="#">
            rack-strip-cookies (2.0.0) lib/rack/strip-cookies.rb:61:in &#39;Rack::StripCookies#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="72" href="#">
            activerecord (8.0.3) lib/active_record/migration.rb:670:in &#39;ActiveRecord::Migration::CheckPending#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="73" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/callbacks.rb:31:in &#39;block in ActionDispatch::Callbacks#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="74" href="#">
            activesupport (8.0.3) lib/active_support/callbacks.rb:100:in &#39;ActiveSupport::Callbacks#run_callbacks&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="75" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/callbacks.rb:30:in &#39;ActionDispatch::Callbacks#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="76" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/executor.rb:16:in &#39;ActionDispatch::Executor#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="77" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/actionable_exceptions.rb:18:in &#39;ActionDispatch::ActionableExceptions#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="78" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/debug_exceptions.rb:31:in &#39;ActionDispatch::DebugExceptions#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="79" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/show_exceptions.rb:32:in &#39;ActionDispatch::ShowExceptions#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="80" href="#">
            railties (8.0.3) lib/rails/rack/logger.rb:41:in &#39;Rails::Rack::Logger#call_app&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="81" href="#">
            railties (8.0.3) lib/rails/rack/logger.rb:29:in &#39;Rails::Rack::Logger#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="82" href="#">
            ahoy (5d10839c3997) lib/ahoy/engine.rb:22:in &#39;Rails::Rack::Logger#call_with_quiet_ahoy&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="83" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/remote_ip.rb:96:in &#39;ActionDispatch::RemoteIp#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="84" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/request_id.rb:34:in &#39;ActionDispatch::RequestId#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="85" href="#">
            rack (3.2.1) lib/rack/method_override.rb:28:in &#39;Rack::MethodOverride#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="86" href="#">
            rack (3.2.1) lib/rack/runtime.rb:24:in &#39;Rack::Runtime#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="87" href="#">
            activesupport (8.0.3) lib/active_support/cache/strategy/local_cache_middleware.rb:29:in &#39;ActiveSupport::Cache::Strategy::LocalCache::Middleware#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="88" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/server_timing.rb:61:in &#39;block in ActionDispatch::ServerTiming#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="89" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/server_timing.rb:26:in &#39;ActionDispatch::ServerTiming::Subscriber#collect_events&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="90" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/server_timing.rb:60:in &#39;ActionDispatch::ServerTiming#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="91" href="#">
            hotwire-livereload (2.0.0) lib/hotwire/livereload/middleware.rb:10:in &#39;Hotwire::Livereload::Middleware#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="92" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/executor.rb:16:in &#39;ActionDispatch::Executor#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="93" href="#">
            propshaft (1.3.1) lib/propshaft/server.rb:37:in &#39;Propshaft::Server#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="94" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/static.rb:27:in &#39;ActionDispatch::Static#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="95" href="#">
            rack (3.2.1) lib/rack/sendfile.rb:114:in &#39;Rack::Sendfile#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="96" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/host_authorization.rb:143:in &#39;ActionDispatch::HostAuthorization#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="97" href="#">
            rack-reverse-proxy (06f21feb6afb) lib/rack_reverse_proxy/roundtrip.rb:19:in &#39;RackReverseProxy::RoundTrip#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="98" href="#">
            rack-reverse-proxy (06f21feb6afb) lib/rack_reverse_proxy/middleware.rb:27:in &#39;RackReverseProxy::Middleware#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="99" href="#">
            rack-cors (3.0.0) lib/rack/cors.rb:102:in &#39;Rack::Cors#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="100" href="#">
            railties (8.0.3) lib/rails/engine.rb:535:in &#39;Rails::Engine#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="101" href="#">
            puma (7.0.4) lib/puma/configuration.rb:299:in &#39;Puma::Configuration::ConfigMiddleware#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="102" href="#">
            puma (7.0.4) lib/puma/request.rb:101:in &#39;block in Puma::Request#handle_request&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="103" href="#">
            puma (7.0.4) lib/puma/thread_pool.rb:346:in &#39;Puma::ThreadPool#with_force_shutdown&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="104" href="#">
            puma (7.0.4) lib/puma/request.rb:100:in &#39;Puma::Request#handle_request&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="105" href="#">
            puma (7.0.4) lib/puma/server.rb:506:in &#39;Puma::Server#process_client&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="106" href="#">
            puma (7.0.4) lib/puma/server.rb:265:in &#39;block in Puma::Server#run&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="107" href="#">
            puma (7.0.4) lib/puma/thread_pool.rb:173:in &#39;block in Puma::ThreadPool#spawn_thread&#39;
          </a>
          <br>
      </code>
    </div>
    <div id="Full-Trace-2" style="display: none;">
      <code class="traces">
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="0" href="#">
            dry-types (1.8.3) lib/dry/types/constrained.rb:37:in &#39;Dry::Types::Constrained#call_unsafe&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="1" href="#">
            dry-types (1.8.3) lib/dry/types/schema/key.rb:44:in &#39;Dry::Types::Schema::Key#call_unsafe&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="2" href="#">
            dry-types (1.8.3) lib/dry/types/schema.rb:328:in &#39;block in Dry::Types::Schema#resolve_unsafe&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="3" href="#">
            dry-types (1.8.3) lib/dry/types/schema.rb:322:in &#39;Hash#each&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="4" href="#">
            dry-types (1.8.3) lib/dry/types/schema.rb:322:in &#39;Dry::Types::Schema#resolve_unsafe&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="5" href="#">
            dry-types (1.8.3) lib/dry/types/schema.rb:60:in &#39;Dry::Types::Schema#call_unsafe&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="6" href="#">
            dry-types (1.8.3) lib/dry/types/constructor.rb:80:in &#39;Dry::Types::Constructor#call_unsafe&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="7" href="#">
            dry-struct (1.8.0) lib/dry/struct/class_interface.rb:254:in &#39;Dry::Struct::ClassInterface#new&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="8" href="#">
            app/controllers/analytics/hubspot_companies_controller.rb:14:in &#39;Analytics::HubspotCompaniesController#show&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="9" href="#">
            actionpack (8.0.3) lib/action_controller/metal/basic_implicit_render.rb:8:in &#39;ActionController::BasicImplicitRender#send_action&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="10" href="#">
            actionpack (8.0.3) lib/abstract_controller/base.rb:215:in &#39;AbstractController::Base#process_action&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="11" href="#">
            actionpack (8.0.3) lib/action_controller/metal/rendering.rb:193:in &#39;ActionController::Rendering#process_action&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="12" href="#">
            actionpack (8.0.3) lib/abstract_controller/callbacks.rb:261:in &#39;block in AbstractController::Callbacks#process_action&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="13" href="#">
            activesupport (8.0.3) lib/active_support/callbacks.rb:120:in &#39;block in ActiveSupport::Callbacks#run_callbacks&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="14" href="#">
            audited (5.8.0) lib/audited/sweeper.rb:16:in &#39;Audited::Sweeper#around&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="15" href="#">
            activesupport (8.0.3) lib/active_support/callbacks.rb:129:in &#39;block in ActiveSupport::Callbacks#run_callbacks&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="16" href="#">
            turbo-rails (2.0.16) lib/turbo-rails.rb:24:in &#39;Turbo.with_request_id&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="17" href="#">
            turbo-rails (2.0.16) app/controllers/concerns/turbo/request_id_tracking.rb:10:in &#39;Turbo::RequestIdTracking#turbo_tracking_request_id&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="18" href="#">
            activesupport (8.0.3) lib/active_support/callbacks.rb:129:in &#39;block in ActiveSupport::Callbacks#run_callbacks&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="19" href="#">
            audited (5.8.0) lib/audited/sweeper.rb:16:in &#39;Audited::Sweeper#around&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="20" href="#">
            activesupport (8.0.3) lib/active_support/callbacks.rb:129:in &#39;block in ActiveSupport::Callbacks#run_callbacks&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="21" href="#">
            ahoy (5d10839c3997) lib/ahoy/controller.rb:50:in &#39;Ahoy::Controller#set_ahoy_request_store&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="22" href="#">
            activesupport (8.0.3) lib/active_support/callbacks.rb:129:in &#39;block in ActiveSupport::Callbacks#run_callbacks&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="23" href="#">
            activesupport (8.0.3) lib/active_support/callbacks.rb:140:in &#39;ActiveSupport::Callbacks#run_callbacks&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="24" href="#">
            actionpack (8.0.3) lib/abstract_controller/callbacks.rb:260:in &#39;AbstractController::Callbacks#process_action&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="25" href="#">
            actionpack (8.0.3) lib/action_controller/metal/rescue.rb:27:in &#39;ActionController::Rescue#process_action&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="26" href="#">
            actionpack (8.0.3) lib/action_controller/metal/instrumentation.rb:76:in &#39;block in ActionController::Instrumentation#process_action&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="27" href="#">
            activesupport (8.0.3) lib/active_support/notifications.rb:210:in &#39;block in ActiveSupport::Notifications.instrument&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="28" href="#">
            activesupport (8.0.3) lib/active_support/notifications/instrumenter.rb:58:in &#39;ActiveSupport::Notifications::Instrumenter#instrument&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="29" href="#">
            activesupport (8.0.3) lib/active_support/notifications.rb:210:in &#39;ActiveSupport::Notifications.instrument&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="30" href="#">
            actionpack (8.0.3) lib/action_controller/metal/instrumentation.rb:75:in &#39;ActionController::Instrumentation#process_action&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="31" href="#">
            actionpack (8.0.3) lib/action_controller/metal/params_wrapper.rb:259:in &#39;ActionController::ParamsWrapper#process_action&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="32" href="#">
            activerecord (8.0.3) lib/active_record/railties/controller_runtime.rb:39:in &#39;ActiveRecord::Railties::ControllerRuntime#process_action&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="33" href="#">
            actionpack (8.0.3) lib/abstract_controller/base.rb:152:in &#39;AbstractController::Base#process&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="34" href="#">
            actionview (8.0.3) lib/action_view/rendering.rb:40:in &#39;ActionView::Rendering#process&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="35" href="#">
            actionpack (8.0.3) lib/action_controller/metal.rb:252:in &#39;ActionController::Metal#dispatch&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="36" href="#">
            actionpack (8.0.3) lib/action_controller/metal.rb:335:in &#39;ActionController::Metal.dispatch&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="37" href="#">
            actionpack (8.0.3) lib/action_dispatch/routing/route_set.rb:67:in &#39;ActionDispatch::Routing::RouteSet::Dispatcher#dispatch&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="38" href="#">
            actionpack (8.0.3) lib/action_dispatch/routing/route_set.rb:50:in &#39;ActionDispatch::Routing::RouteSet::Dispatcher#serve&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="39" href="#">
            actionpack (8.0.3) lib/action_dispatch/journey/router.rb:53:in &#39;block in ActionDispatch::Journey::Router#serve&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="40" href="#">
            actionpack (8.0.3) lib/action_dispatch/journey/router.rb:133:in &#39;block in ActionDispatch::Journey::Router#find_routes&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="41" href="#">
            actionpack (8.0.3) lib/action_dispatch/journey/router.rb:126:in &#39;Array#each&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="42" href="#">
            actionpack (8.0.3) lib/action_dispatch/journey/router.rb:126:in &#39;ActionDispatch::Journey::Router#find_routes&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="43" href="#">
            actionpack (8.0.3) lib/action_dispatch/journey/router.rb:34:in &#39;ActionDispatch::Journey::Router#serve&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="44" href="#">
            actionpack (8.0.3) lib/action_dispatch/routing/route_set.rb:908:in &#39;ActionDispatch::Routing::RouteSet#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="45" href="#">
            omniauth (2.1.3) lib/omniauth/strategy.rb:202:in &#39;OmniAuth::Strategy#call!&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="46" href="#">
            omniauth (2.1.3) lib/omniauth/strategy.rb:169:in &#39;OmniAuth::Strategy#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="47" href="#">
            omniauth (2.1.3) lib/omniauth/strategy.rb:202:in &#39;OmniAuth::Strategy#call!&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="48" href="#">
            omniauth (2.1.3) lib/omniauth/strategy.rb:169:in &#39;OmniAuth::Strategy#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="49" href="#">
            omniauth (2.1.3) lib/omniauth/strategy.rb:202:in &#39;OmniAuth::Strategy#call!&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="50" href="#">
            omniauth (2.1.3) lib/omniauth/strategy.rb:169:in &#39;OmniAuth::Strategy#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="51" href="#">
            omniauth (2.1.3) lib/omniauth/builder.rb:44:in &#39;OmniAuth::Builder#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="52" href="#">
            rack-attack (6.7.0) lib/rack/attack.rb:127:in &#39;Rack::Attack#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="53" href="#">
            jsonapi-rails (0.4.1) lib/jsonapi/rails/filter_media_type.rb:16:in &#39;JSONAPI::Rails::FilterMediaType#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="54" href="#">
            lib/clearance/impersonation.rb:11:in &#39;Clearance::Impersonation#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="55" href="#">
            lib/unique_session_cookie.rb:14:in &#39;UniqueSessionCookie#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="56" href="#">
            rack-utm (0.0.2) lib/rack-utm.rb:59:in &#39;Rack::Utm#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="57" href="#">
            rack (3.2.1) lib/rack/deflater.rb:47:in &#39;Rack::Deflater#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="58" href="#">
            rack-canonical-host (1.3.0) lib/rack/canonical_host.rb:24:in &#39;Rack::CanonicalHost#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="59" href="#">
            rack (3.2.1) lib/rack/static.rb:162:in &#39;Rack::Static#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="60" href="#">
            clearance (2.10.0) lib/clearance/rack_session.rb:23:in &#39;Clearance::RackSession#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="61" href="#">
            lib/cookie_consent.rb:13:in &#39;CookieConsent#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="62" href="#">
            rack (3.2.1) lib/rack/tempfile_reaper.rb:20:in &#39;Rack::TempfileReaper#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="63" href="#">
            rack (3.2.1) lib/rack/etag.rb:29:in &#39;Rack::ETag#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="64" href="#">
            rack (3.2.1) lib/rack/conditional_get.rb:31:in &#39;Rack::ConditionalGet#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="65" href="#">
            rack (3.2.1) lib/rack/head.rb:15:in &#39;Rack::Head#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="66" href="#">
            actionpack (8.0.3) lib/action_dispatch/http/permissions_policy.rb:38:in &#39;ActionDispatch::PermissionsPolicy::Middleware#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="67" href="#">
            actionpack (8.0.3) lib/action_dispatch/http/content_security_policy.rb:38:in &#39;ActionDispatch::ContentSecurityPolicy::Middleware#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="68" href="#">
            rack-session (2.1.1) lib/rack/session/abstract/id.rb:274:in &#39;Rack::Session::Abstract::Persisted#context&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="69" href="#">
            rack-session (2.1.1) lib/rack/session/abstract/id.rb:268:in &#39;Rack::Session::Abstract::Persisted#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="70" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/cookies.rb:706:in &#39;ActionDispatch::Cookies#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="71" href="#">
            rack-strip-cookies (2.0.0) lib/rack/strip-cookies.rb:61:in &#39;Rack::StripCookies#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="72" href="#">
            activerecord (8.0.3) lib/active_record/migration.rb:670:in &#39;ActiveRecord::Migration::CheckPending#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="73" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/callbacks.rb:31:in &#39;block in ActionDispatch::Callbacks#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="74" href="#">
            activesupport (8.0.3) lib/active_support/callbacks.rb:100:in &#39;ActiveSupport::Callbacks#run_callbacks&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="75" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/callbacks.rb:30:in &#39;ActionDispatch::Callbacks#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="76" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/executor.rb:16:in &#39;ActionDispatch::Executor#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="77" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/actionable_exceptions.rb:18:in &#39;ActionDispatch::ActionableExceptions#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="78" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/debug_exceptions.rb:31:in &#39;ActionDispatch::DebugExceptions#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="79" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/show_exceptions.rb:32:in &#39;ActionDispatch::ShowExceptions#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="80" href="#">
            railties (8.0.3) lib/rails/rack/logger.rb:41:in &#39;Rails::Rack::Logger#call_app&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="81" href="#">
            railties (8.0.3) lib/rails/rack/logger.rb:29:in &#39;Rails::Rack::Logger#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="82" href="#">
            ahoy (5d10839c3997) lib/ahoy/engine.rb:22:in &#39;Rails::Rack::Logger#call_with_quiet_ahoy&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="83" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/remote_ip.rb:96:in &#39;ActionDispatch::RemoteIp#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="84" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/request_id.rb:34:in &#39;ActionDispatch::RequestId#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="85" href="#">
            rack (3.2.1) lib/rack/method_override.rb:28:in &#39;Rack::MethodOverride#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="86" href="#">
            rack (3.2.1) lib/rack/runtime.rb:24:in &#39;Rack::Runtime#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="87" href="#">
            activesupport (8.0.3) lib/active_support/cache/strategy/local_cache_middleware.rb:29:in &#39;ActiveSupport::Cache::Strategy::LocalCache::Middleware#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="88" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/server_timing.rb:61:in &#39;block in ActionDispatch::ServerTiming#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="89" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/server_timing.rb:26:in &#39;ActionDispatch::ServerTiming::Subscriber#collect_events&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="90" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/server_timing.rb:60:in &#39;ActionDispatch::ServerTiming#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="91" href="#">
            hotwire-livereload (2.0.0) lib/hotwire/livereload/middleware.rb:10:in &#39;Hotwire::Livereload::Middleware#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="92" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/executor.rb:16:in &#39;ActionDispatch::Executor#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="93" href="#">
            propshaft (1.3.1) lib/propshaft/server.rb:37:in &#39;Propshaft::Server#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="94" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/static.rb:27:in &#39;ActionDispatch::Static#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="95" href="#">
            rack (3.2.1) lib/rack/sendfile.rb:114:in &#39;Rack::Sendfile#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="96" href="#">
            actionpack (8.0.3) lib/action_dispatch/middleware/host_authorization.rb:143:in &#39;ActionDispatch::HostAuthorization#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="97" href="#">
            rack-reverse-proxy (06f21feb6afb) lib/rack_reverse_proxy/roundtrip.rb:19:in &#39;RackReverseProxy::RoundTrip#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="98" href="#">
            rack-reverse-proxy (06f21feb6afb) lib/rack_reverse_proxy/middleware.rb:27:in &#39;RackReverseProxy::Middleware#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="99" href="#">
            rack-cors (3.0.0) lib/rack/cors.rb:102:in &#39;Rack::Cors#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="100" href="#">
            railties (8.0.3) lib/rails/engine.rb:535:in &#39;Rails::Engine#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="101" href="#">
            puma (7.0.4) lib/puma/configuration.rb:299:in &#39;Puma::Configuration::ConfigMiddleware#call&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="102" href="#">
            puma (7.0.4) lib/puma/request.rb:101:in &#39;block in Puma::Request#handle_request&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="103" href="#">
            puma (7.0.4) lib/puma/thread_pool.rb:346:in &#39;Puma::ThreadPool#with_force_shutdown&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="104" href="#">
            puma (7.0.4) lib/puma/request.rb:100:in &#39;Puma::Request#handle_request&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="105" href="#">
            puma (7.0.4) lib/puma/server.rb:506:in &#39;Puma::Server#process_client&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="106" href="#">
            puma (7.0.4) lib/puma/server.rb:265:in &#39;block in Puma::Server#run&#39;
          </a>
          <br>
          <a class="trace-frames trace-frames-2" data-exception-object-id="253320" data-frame-id="107" href="#">
            puma (7.0.4) lib/puma/thread_pool.rb:173:in &#39;block in Puma::ThreadPool#spawn_thread&#39;
          </a>
          <br>
      </code>
    </div>

  <script>
    (function() {
      var traceFrames = document.getElementsByClassName('trace-frames-2');
      var selectedFrame, currentSource = document.getElementById('frame-source-2-0');

      // Add click listeners for all stack frames
      for (var i = 0; i < traceFrames.length; i++) {
        traceFrames[i].addEventListener('click', function(e) {
          e.preventDefault();
          var target = e.target;
          var frame_id = target.dataset.frameId;

          if (selectedFrame) {
            selectedFrame.className = selectedFrame.className.replace("selected", "");
          }

          target.className += " selected";
          selectedFrame = target;

          // Change the extracted source code
          changeSourceExtract(frame_id);
        });

        function changeSourceExtract(frame_id) {
          var el = document.getElementById('frame-source-2-' + frame_id);
          if (currentSource && el) {
            currentSource.className += " hidden";
            el.className = el.className.replace(" hidden", "");
            currentSource = el;
          }
        }
      }
    })();
  </script>
</div>

    </div>

  <h2 class="request-heading">Request</h2>
  <p><b>Parameters</b>:</p> <pre>{&quot;subdomain&quot; =&gt; &quot;www&quot;, &quot;id&quot; =&gt; &quot;3948589892&quot;}
</pre>

<div class="details">
  <div class="summary"><a href="#" onclick="return toggleSessionDump()">Toggle session dump</a></div>
  <div id="session_dump" class="hidden"><pre></pre></div>
</div>

<div class="details">
  <div class="summary"><a href="#" onclick="return toggleEnvDump()">Toggle env dump</a></div>
  <div id="env_dump" class="hidden"><pre>GATEWAY_INTERFACE: &quot;CGI/1.2&quot;
HTTP_ACCEPT: &quot;*/*&quot;
HTTP_ACCEPT_ENCODING: &quot;gzip&quot;
HTTP_X_FORWARDED_FOR: &quot;127.0.0.1&quot;
ORIGINAL_SCRIPT_NAME: &quot;&quot;
REMOTE_ADDR: &quot;127.0.0.1&quot;
SERVER_NAME: &quot;www.carriersource.test&quot;
SERVER_PROTOCOL: &quot;HTTP/1.1&quot;</pre></div>
</div>

<h2 class="response-heading">Response</h2>
<p><b>Headers</b>:</p> <pre>None</pre>

</main>


</body>
</html>
