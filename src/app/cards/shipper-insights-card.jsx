import React, { useState, useEffect } from "react";
import { 
  <PERSON>, 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>lex,
  Box
} from "@hubspot/ui-extensions";
import { hubspot } from "@hubspot/ui-extensions";

hubspot.extend(({ context, runServerlessFunction, actions }) => (
  <ShipperInsightsCard context={context} />
));

const ShipperInsightsCard = ({ context }) => {
  const [insights, setInsights] = useState(null);
  const [status, setStatus] = useState("pending");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [polling, setPolling] = useState(false);

  const companyId = context.crm.objectId;

  // Fetch insights data
  const fetchInsights = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await hubspot.fetch(
        `https://www.carriersource.io/analytics/shipper-intent-insights/${companyId}`
      );
      
      if (!response.ok) {
        throw new Error(`API request failed: ${response.status}`);
      }
      
      const data = await response.json();
      setInsights(data);
      setStatus(data.status || "pending");
      
      // Start polling if status is queued or processing
      if (data.status === "queued" || data.status === "processing") {
        startPolling();
      }
    } catch (err) {
      setError(err.message);
      setStatus("error");
    } finally {
      setLoading(false);
    }
  };

  // Generate insights (trigger API)
  const generateInsights = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await hubspot.fetch(
        `https://www.carriersource.io/analytics/shipper-intent-insights/${companyId}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          }
        }
      );
      
      if (!response.ok) {
        throw new Error(`Failed to generate insights: ${response.status}`);
      }
      
      const data = await response.json();
      setInsights(data);
      setStatus(data.status || "queued");
      
      if (data.status === "queued" || data.status === "processing") {
        startPolling();
      }
    } catch (err) {
      setError(err.message);
      setStatus("error");
    } finally {
      setLoading(false);
    }
  };

  // Polling for status updates
  const startPolling = () => {
    if (polling) return;
    
    setPolling(true);
    const pollInterval = setInterval(async () => {
      try {
        const response = await hubspot.fetch(
          `https://www.carriersource.io/analytics/shipper-intent-insights/${companyId}`
        );
        
        if (response.ok) {
          const data = await response.json();
          setInsights(data);
          setStatus(data.status || "pending");
          
          if (data.status === "complete" || data.status === "error") {
            clearInterval(pollInterval);
            setPolling(false);
          }
        }
      } catch (err) {
        console.error("Polling error:", err);
      }
    }, 3000); // Poll every 3 seconds

    // Stop polling after 5 minutes
    setTimeout(() => {
      clearInterval(pollInterval);
      setPolling(false);
    }, 300000);
  };

  // Load initial data
  useEffect(() => {
    fetchInsights();
  }, [companyId]);

  // Render based on status
  const renderContent = () => {
    if (loading && !polling) {
      return (
        <Flex direction="column" align="center" gap="medium">
          <LoadingSpinner />
          <Text>Loading insights...</Text>
        </Flex>
      );
    }

    if (error) {
      return (
        <Alert title="Error" variant="error">
          <Text>{error}</Text>
          <Button onClick={fetchInsights} variant="secondary">
            Retry
          </Button>
        </Alert>
      );
    }

    switch (status) {
      case "pending":
        return (
          <Flex direction="column" gap="medium">
            <Text>No insights available for this company yet.</Text>
            <Button onClick={generateInsights} variant="primary">
              Generate Insights
            </Button>
          </Flex>
        );

      case "queued":
      case "processing":
        return (
          <Flex direction="column" align="center" gap="medium">
            <LoadingSpinner />
            <Text>
              {status === "queued" 
                ? "Insights generation queued..." 
                : "Processing insights..."}
            </Text>
            <Button disabled variant="secondary">
              {status === "queued" ? "In Queue" : "Processing"}
            </Button>
          </Flex>
        );

      case "complete":
        return (
          <Flex direction="column" gap="medium">
            <Text variant="microcopy">Insights generated successfully</Text>
            <Divider />
            {insights?.data && (
              <Box>
                <Text format={{ fontWeight: "bold" }}>Shipper Intent Score:</Text>
                <Text>{insights.data.intentScore || "N/A"}</Text>
                
                <Text format={{ fontWeight: "bold" }}>Risk Level:</Text>
                <Text>{insights.data.riskLevel || "N/A"}</Text>
                
                <Text format={{ fontWeight: "bold" }}>Recommendations:</Text>
                <Text>{insights.data.recommendations || "No recommendations available"}</Text>
              </Box>
            )}
            <Button onClick={generateInsights} variant="secondary">
              Regenerate Insights
            </Button>
          </Flex>
        );

      default:
        return (
          <Alert title="Unknown Status" variant="warning">
            <Text>Unexpected status: {status}</Text>
            <Button onClick={fetchInsights} variant="secondary">
              Refresh
            </Button>
          </Alert>
        );
    }
  };

  return (
    <Flex direction="column" gap="medium">
      <Text format={{ fontWeight: "bold" }}>Shipper Insights</Text>
      {renderContent()}
    </Flex>
  );
};