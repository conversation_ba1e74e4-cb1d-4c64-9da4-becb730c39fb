{"uid": "get_started_app", "type": "app", "config": {"description": "A basic static token app for testing", "name": "My Get Started app", "distribution": "private", "auth": {"type": "static", "requiredScopes": ["crm.objects.contacts.read", "crm.objects.contacts.write"], "optionalScopes": [], "conditionallyRequiredScopes": []}, "permittedUrls": {"fetch": ["https://api.hubapi.com", "https://www.carriersource.io"], "iframe": [], "img": []}, "support": {"supportEmail": "<EMAIL>", "documentationUrl": "https://example.com/docs", "supportUrl": "https://example.com/support", "supportPhone": "+18005555555"}}}